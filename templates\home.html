<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>知识库管理系统 - 首页</title>
  <!-- 引入Element UI样式 -->
  <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
  <!-- 引入Vue.js -->
  <script src="https://unpkg.com/vue@2/dist/vue.js"></script>
  <!-- 引入Element UI组件库 -->
  <script src="https://unpkg.com/element-ui/lib/index.js"></script>
  <!-- 引入axios -->
  <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
  
  <style>
    body {
      margin: 0;
      padding: 0;
      background: #ffffff;
      font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
      min-height: 100vh;
    }

    .main-container {
      height: 100vh;
    }

    .nav-menu {
      height: calc(100% - 60px);
      margin-top: 60px;
      border-right: none;
    }

    .toolbar {
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 20px;
      padding: 15px 20px;
      background: white;
      height: 60px;
      box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
      position: relative;
      z-index: 1;
    }

    .home-content {
      padding: 20px;
      height: calc(100vh - 80px);
      overflow-y: auto;
      background: #f5f7fa;
    }

    .header {
      text-align: center;
      margin-bottom: 30px;
      background: white;
      padding: 30px 20px;
      border-radius: 8px;
      box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
    }

    .header h1 {
      color: #303133;
      font-size: 32px;
      font-weight: 600;
      margin: 0 0 10px 0;
    }

    .header p {
      color: #606266;
      font-size: 16px;
      margin: 0;
    }

    .datasets-grid {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 20px;
      margin-bottom: 20px;
    }

    .dataset-card {
      background: white;
      border-radius: 8px;
      padding: 20px;
      box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
      transition: all 0.3s ease;
      cursor: pointer;
      position: relative;
      overflow: hidden;
      border: 1px solid #ebeef5;
    }

    .dataset-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 25px rgba(0,0,0,0.15);
      border-color: #409EFF;
    }

    .dataset-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 3px;
      background: linear-gradient(90deg, #409EFF, #67C23A);
    }

    .dataset-header {
      display: flex;
      align-items: center;
      margin-bottom: 20px;
    }

    .dataset-avatar {
      width: 50px;
      height: 50px;
      border-radius: 8px;
      background: linear-gradient(135deg, #409EFF, #67C23A);
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 12px;
      color: white;
      font-size: 20px;
      font-weight: bold;
    }

    .dataset-info h3 {
      margin: 0 0 8px 0;
      font-size: 20px;
      font-weight: 600;
      color: #303133;
      line-height: 1.2;
    }

    .dataset-info p {
      margin: 0;
      color: #909399;
      font-size: 14px;
      line-height: 1.4;
    }

    .dataset-description {
      color: #606266;
      font-size: 14px;
      line-height: 1.6;
      margin-bottom: 20px;
      min-height: 40px;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }

    .dataset-stats {
      display: flex;
      justify-content: space-between;
      margin-bottom: 20px;
    }

    .stat-item {
      text-align: center;
      flex: 1;
    }

    .stat-number {
      font-size: 20px;
      font-weight: bold;
      color: #409EFF;
      margin-bottom: 4px;
    }

    .stat-label {
      font-size: 12px;
      color: #909399;
    }

    .dataset-meta {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-top: 20px;
      border-top: 1px solid #f0f0f0;
    }

    .update-time {
      font-size: 12px;
      color: #c0c4cc;
    }

    .enter-btn {
      background: #409EFF;
      color: white;
      border: none;
      padding: 6px 16px;
      border-radius: 4px;
      font-size: 12px;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .enter-btn:hover {
      background: #66b1ff;
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4);
    }

    .loading-container {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 200px;
    }

    .empty-state {
      text-align: center;
      padding: 60px 20px;
      color: #909399;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
    }

    .empty-state i {
      font-size: 64px;
      margin-bottom: 20px;
      opacity: 0.6;
      color: #C0C4CC;
    }

    .empty-state h3 {
      font-size: 18px;
      margin-bottom: 10px;
      color: #606266;
    }

    .empty-state p {
      font-size: 14px;
      opacity: 0.8;
    }

    /* 响应式设计 */
    @media (max-width: 1200px) {
      .datasets-grid {
        grid-template-columns: repeat(3, 1fr);
      }
    }

    @media (max-width: 900px) {
      .datasets-grid {
        grid-template-columns: repeat(2, 1fr);
      }
    }

    @media (max-width: 768px) {
      .header h1 {
        font-size: 24px;
      }

      .datasets-grid {
        grid-template-columns: 1fr;
        gap: 15px;
      }

      .dataset-card {
        padding: 15px;
      }

      .home-content {
        padding: 15px;
      }
    }
  </style>
</head>
<body>
  <div id="app">
    <el-container class="main-container">
      <!-- 左侧导航 -->
      <el-aside width="240px" style="background:#f5f7fa">
        <el-menu
          class="nav-menu"
          :default-active="activeMenu"
          background-color="#f5f7fa"
          text-color="#606266"
          active-text-color="#409EFF"
          @select="handleMenuSelect">
          <el-menu-item index="home">
            <i class="el-icon-s-home"></i>
            <span>首页</span>
          </el-menu-item>
          <el-menu-item index="docs">
            <i class="el-icon-folder-opened"></i>
            <span>文档库</span>
          </el-menu-item>
          <el-menu-item index="images">
            <i class="el-icon-picture"></i>
            <span>图片管理</span>
          </el-menu-item>
          <el-menu-item index="audit">
            <i class="el-icon-view"></i>
            <span>图片审核</span>
          </el-menu-item>
        </el-menu>
      </el-aside>

      <!-- 右侧内容 -->
      <el-main style="padding:0">
        <!-- 顶部工具栏 -->
        <div class="toolbar">
          <h2 style="margin: 0; color: #303133;">知识库管理系统</h2>
        </div>

        <!-- 主要内容区域 -->
        <div class="home-content">
          <!-- 加载状态 -->
          <div v-if="loading" class="loading-container">
            <el-loading-spinner></el-loading-spinner>
          </div>

          <!-- 知识库网格 -->
          <div v-else-if="datasets.length > 0" class="datasets-grid">
        <div 
          v-for="dataset in datasets" 
          :key="dataset.id" 
          class="dataset-card"
          @click="enterDataset(dataset)"
        >
          <div class="dataset-header">
            <div class="dataset-avatar">
              {{ dataset.name.charAt(0) }}
            </div>
            <div class="dataset-info">
              <h3>{{ dataset.name }}</h3>
              <p>{{ formatUpdateTime(dataset.updateTime) }}</p>
            </div>
          </div>
          
          <div class="dataset-description">
            {{ dataset.intro || '暂无描述' }}
          </div>
          
          <div class="dataset-stats">
            <div class="stat-item">
              <div class="stat-number">{{ dataset.docCount }}</div>
              <div class="stat-label">文档总数</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">{{ dataset.auditedCount }}</div>
              <div class="stat-label">已审核</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">{{ ((dataset.auditedCount / dataset.docCount) * 100).toFixed(0) || 0 }}%</div>
              <div class="stat-label">审核率</div>
            </div>
          </div>
          
          <div class="dataset-meta">
            <span class="update-time">
              更新于 {{ formatUpdateTime(dataset.updateTime) }}
            </span>
            <button class="enter-btn">
              进入管理
            </button>
          </div>
        </div>
      </div>

          <!-- 空状态 -->
          <div v-else class="empty-state">
            <i class="el-icon-folder-opened"></i>
            <h3>暂无知识库</h3>
            <p>还没有创建任何知识库，请先创建知识库</p>
          </div>
        </div>
      </el-main>
    </el-container>
  </div>

  <script>
    new Vue({
      el: '#app',
      data() {
        return {
          loading: true,
          datasets: [],
          activeMenu: 'home'
        }
      },
      mounted() {
        this.loadDatasets();
      },
      methods: {
        async loadDatasets() {
          try {
            this.loading = true;
            const response = await axios.get('/getHomeDatasetsInfo/');
            if (response.data.code === 200) {
              this.datasets = response.data.data;
            } else {
              this.$message.error('获取知识库信息失败');
            }
          } catch (error) {
            console.error('加载知识库失败:', error);
            this.$message.error('网络错误，请稍后重试');
          } finally {
            this.loading = false;
          }
        },
        
        enterDataset(dataset) {
          // 跳转到文档库管理页面
          window.location.href = `/docs/?datasetId=${dataset.id}`;
        },

        handleMenuSelect(index) {
          this.activeMenu = index;
          switch(index) {
            case 'home':
              window.location.href = '/';
              break;
            case 'docs':
              window.location.href = '/docs/';
              break;
            case 'images':
              window.location.href = '/h5_images/';
              break;
            case 'audit':
              window.location.href = '/h5_images_audit_wh/';
              break;
          }
        },
        
        formatUpdateTime(timeStr) {
          if (!timeStr) return '未知';
          const date = new Date(timeStr);
          const now = new Date();
          const diff = now - date;
          
          if (diff < 60000) return '刚刚';
          if (diff < 3600000) return Math.floor(diff / 60000) + '分钟前';
          if (diff < 86400000) return Math.floor(diff / 3600000) + '小时前';
          if (diff < 2592000000) return Math.floor(diff / 86400000) + '天前';
          
          return date.toLocaleDateString('zh-CN');
        }
      }
    });
  </script>
</body>
</html>
