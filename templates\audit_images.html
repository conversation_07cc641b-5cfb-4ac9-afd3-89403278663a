<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片审核 - 知识库管理系统</title>
    <!-- 引入Element UI样式 -->
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <!-- 引入统一样式 -->
    <link rel="stylesheet" href="/static/css/common.css">
    <!-- 引入Vue.js -->
    <script src="https://unpkg.com/vue@2/dist/vue.js"></script>
    <!-- 引入Element UI组件库 -->
    <script src="https://unpkg.com/element-ui/lib/index.js"></script>
    <!-- 引入axios -->
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
    
    <style>
        .audit-header {
            background: var(--primary-gradient);
            color: white;
            padding: 20px;
            margin-bottom: 20px;
            border-radius: 8px;
        }
        
        .audit-header h2 {
            margin: 0 0 10px 0;
            font-size: 24px;
            font-weight: 600;
        }
        
        .audit-header p {
            margin: 0;
            opacity: 0.9;
            font-size: 14px;
        }
        
        .back-btn {
            background: var(--background-white);
            color: var(--primary-color);
            border: 1px solid var(--primary-color);
            padding: 8px 16px;
            border-radius: 6px;
            text-decoration: none;
            font-size: 14px;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .back-btn:hover {
            background: var(--primary-color);
            color: white;
            transform: translateY(-2px);
        }
        
        .audit-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .stat-card {
            background: var(--background-white);
            padding: 20px;
            border-radius: 8px;
            box-shadow: var(--shadow-light);
            text-align: center;
        }
        
        .stat-number {
            font-size: 32px;
            font-weight: bold;
            color: var(--primary-color);
            margin-bottom: 8px;
        }
        
        .stat-label {
            font-size: 14px;
            color: var(--text-secondary);
        }
        
        .filter-section {
            background: var(--background-white);
            padding: 20px;
            border-radius: 8px;
            box-shadow: var(--shadow-light);
            margin-bottom: 20px;
        }
        
        .filter-row {
            display: flex;
            gap: 16px;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .audit-table {
            background: var(--background-white);
            border-radius: 8px;
            box-shadow: var(--shadow-light);
            overflow: hidden;
        }
        
        .table-header {
            padding: 20px;
            border-bottom: 1px solid var(--border-lighter);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .table-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-primary);
        }
        
        .image-preview {
            width: 80px;
            height: 80px;
            object-fit: cover;
            border-radius: 6px;
            cursor: pointer;
            transition: transform 0.3s ease;
        }
        
        .image-preview:hover {
            transform: scale(1.1);
        }
        
        .audit-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-pending {
            background: #fff7e6;
            color: var(--warning-color);
        }
        
        .status-approved {
            background: #f0f9ff;
            color: var(--success-color);
        }
        
        .status-rejected {
            background: #fef0f0;
            color: var(--danger-color);
        }
        
        .audit-actions {
            display: flex;
            gap: 8px;
        }
        
        .btn-approve {
            background: var(--success-color);
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .btn-approve:hover {
            background: #5daf34;
            transform: translateY(-1px);
        }
        
        .btn-reject {
            background: var(--danger-color);
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .btn-reject:hover {
            background: #f45656;
            transform: translateY(-1px);
        }
        
        .pagination-wrapper {
            padding: 20px;
            display: flex;
            justify-content: center;
            background: var(--background-white);
            border-top: 1px solid var(--border-lighter);
        }
        
        /* 图片预览对话框样式 */
        .image-dialog .el-dialog__body {
            text-align: center;
            padding: 20px;
        }
        
        .preview-image {
            max-width: 100%;
            max-height: 500px;
            border-radius: 8px;
            box-shadow: var(--shadow-base);
        }
        
        .image-info {
            margin-top: 16px;
            padding: 16px;
            background: var(--background-base);
            border-radius: 8px;
            text-align: left;
        }
        
        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
        }
        
        .info-label {
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .info-value {
            color: var(--text-secondary);
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .audit-stats {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .filter-row {
                flex-direction: column;
                align-items: stretch;
            }
            
            .table-header {
                flex-direction: column;
                gap: 12px;
                align-items: stretch;
            }
            
            .audit-actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="container">
            <!-- 页面头部 -->
            <div class="audit-header">
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <div>
                        <h2>图片审核</h2>
                        <p>审核知识库中上传的图片内容</p>
                    </div>
                    <a href="/" class="back-btn">
                        <i class="el-icon-arrow-left"></i>
                        返回首页
                    </a>
                </div>
            </div>

            <!-- 统计信息 -->
            <div class="audit-stats">
                <div class="stat-card">
                    <div class="stat-number">{{ stats.total }}</div>
                    <div class="stat-label">总图片数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">{{ stats.pending }}</div>
                    <div class="stat-label">待审核</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">{{ stats.approved }}</div>
                    <div class="stat-label">已通过</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">{{ stats.rejected }}</div>
                    <div class="stat-label">已拒绝</div>
                </div>
            </div>

            <!-- 筛选区域 -->
            <div class="filter-section">
                <div class="filter-row">
                    <el-select v-model="filterStatus" placeholder="筛选状态" style="width: 150px;" @change="filterImages">
                        <el-option label="全部" value=""></el-option>
                        <el-option label="待审核" value="pending"></el-option>
                        <el-option label="已通过" value="approved"></el-option>
                        <el-option label="已拒绝" value="rejected"></el-option>
                    </el-select>
                    
                    <el-select v-model="selectedDataset" placeholder="选择知识库" style="width: 200px;" @change="loadImages">
                        <el-option 
                            v-for="dataset in datasets" 
                            :key="dataset.id" 
                            :label="dataset.name" 
                            :value="dataset.id">
                        </el-option>
                    </el-select>
                    
                    <el-button type="primary" @click="loadImages" :loading="loading">
                        <i class="el-icon-refresh"></i>
                        刷新数据
                    </el-button>
                    
                    <el-button type="success" @click="batchApprove" :disabled="!selectedImages.length">
                        <i class="el-icon-check"></i>
                        批量通过 ({{ selectedImages.length }})
                    </el-button>
                    
                    <el-button type="danger" @click="batchReject" :disabled="!selectedImages.length">
                        <i class="el-icon-close"></i>
                        批量拒绝 ({{ selectedImages.length }})
                    </el-button>
                </div>
            </div>

            <!-- 图片审核列表 -->
            <div class="audit-table">
                <div class="table-header">
                    <div class="table-title">图片审核列表 ({{ filteredImages.length }})</div>
                    <div>
                        <el-checkbox v-model="selectAll" @change="toggleSelectAll">全选</el-checkbox>
                    </div>
                </div>
                
                <el-table 
                    :data="paginatedImages" 
                    style="width: 100%"
                    @selection-change="handleSelectionChange"
                    v-loading="loading">
                    
                    <el-table-column type="selection" width="55"></el-table-column>
                    
                    <el-table-column label="序号" width="80">
                        <template slot-scope="scope">
                            {{ (currentPage - 1) * pageSize + scope.$index + 1 }}
                        </template>
                    </el-table-column>
                    
                    <el-table-column label="图片预览" width="120">
                        <template slot-scope="scope">
                            <img 
                                :src="scope.row.imageUrl" 
                                class="image-preview"
                                @click="previewImage(scope.row)"
                                @error="handleImageError"
                                alt="图片预览">
                        </template>
                    </el-table-column>
                    
                    <el-table-column label="文档信息" min-width="200">
                        <template slot-scope="scope">
                            <div style="font-weight: 500;">{{ scope.row.documentName }}</div>
                            <div style="font-size: 12px; color: var(--text-secondary); margin-top: 4px;">
                                知识库: {{ scope.row.datasetName }}
                            </div>
                        </template>
                    </el-table-column>
                    
                    <el-table-column label="上传者" width="120">
                        <template slot-scope="scope">
                            {{ scope.row.uploader || '未知' }}
                        </template>
                    </el-table-column>
                    
                    <el-table-column label="上传时间" width="150">
                        <template slot-scope="scope">
                            {{ formatDate(scope.row.uploadTime) }}
                        </template>
                    </el-table-column>
                    
                    <el-table-column label="审核状态" width="100">
                        <template slot-scope="scope">
                            <span :class="['audit-status', `status-${scope.row.auditStatus}`]">
                                {{ getStatusText(scope.row.auditStatus) }}
                            </span>
                        </template>
                    </el-table-column>
                    
                    <el-table-column label="操作" width="150">
                        <template slot-scope="scope">
                            <div class="audit-actions" v-if="scope.row.auditStatus === 'pending'">
                                <button 
                                    class="btn-approve" 
                                    @click="approveImage(scope.row)"
                                    :disabled="scope.row.processing">
                                    通过
                                </button>
                                <button 
                                    class="btn-reject" 
                                    @click="rejectImage(scope.row)"
                                    :disabled="scope.row.processing">
                                    拒绝
                                </button>
                            </div>
                            <div v-else>
                                <el-tag :type="scope.row.auditStatus === 'approved' ? 'success' : 'danger'" size="mini">
                                    {{ getStatusText(scope.row.auditStatus) }}
                                </el-tag>
                            </div>
                        </template>
                    </el-table-column>
                </el-table>
                
                <!-- 分页 -->
                <div class="pagination-wrapper">
                    <el-pagination
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                        :current-page="currentPage"
                        :page-sizes="[10, 20, 50, 100]"
                        :page-size="pageSize"
                        layout="total, sizes, prev, pager, next, jumper"
                        :total="filteredImages.length">
                    </el-pagination>
                </div>
            </div>
        </div>

        <!-- 图片预览对话框 -->
        <el-dialog 
            title="图片审核预览" 
            :visible.sync="previewDialogVisible" 
            width="70%"
            class="image-dialog">
            <img :src="currentPreviewImage.imageUrl" class="preview-image" alt="图片预览">
            
            <div class="image-info">
                <div class="info-row">
                    <span class="info-label">文档名称:</span>
                    <span class="info-value">{{ currentPreviewImage.documentName }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">知识库:</span>
                    <span class="info-value">{{ currentPreviewImage.datasetName }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">上传者:</span>
                    <span class="info-value">{{ currentPreviewImage.uploader || '未知' }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">上传时间:</span>
                    <span class="info-value">{{ formatDate(currentPreviewImage.uploadTime) }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">审核状态:</span>
                    <span class="info-value">
                        <span :class="['audit-status', `status-${currentPreviewImage.auditStatus}`]">
                            {{ getStatusText(currentPreviewImage.auditStatus) }}
                        </span>
                    </span>
                </div>
            </div>
            
            <div slot="footer" class="dialog-footer" v-if="currentPreviewImage.auditStatus === 'pending'">
                <el-button @click="previewDialogVisible = false">取消</el-button>
                <el-button type="danger" @click="rejectImageFromPreview">拒绝</el-button>
                <el-button type="success" @click="approveImageFromPreview">通过</el-button>
            </div>
        </el-dialog>
    </div>

    <script>
        new Vue({
            el: '#app',
            data() {
                return {
                    loading: false,
                    datasets: [],
                    images: [],
                    filteredImages: [],
                    selectedDataset: '',
                    filterStatus: '',
                    selectedImages: [],
                    selectAll: false,
                    currentPage: 1,
                    pageSize: 20,
                    previewDialogVisible: false,
                    currentPreviewImage: {},
                    stats: {
                        total: 0,
                        pending: 0,
                        approved: 0,
                        rejected: 0
                    }
                }
            },
            computed: {
                paginatedImages() {
                    const start = (this.currentPage - 1) * this.pageSize;
                    const end = start + this.pageSize;
                    return this.filteredImages.slice(start, end);
                }
            },
            mounted() {
                this.loadDatasets();
                this.loadImages();
            },
            methods: {
                async loadDatasets() {
                    try {
                        const response = await axios.get('/getDatasList/');
                        if (response.data.code === 200) {
                            this.datasets = response.data.data;
                        }
                    } catch (error) {
                        console.error('加载知识库失败:', error);
                        this.$message.error('加载知识库失败');
                    }
                },
                
                async loadImages() {
                    try {
                        this.loading = true;
                        const params = this.selectedDataset ? `?datasetId=${this.selectedDataset}` : '';
                        const response = await axios.get(`/getAuditImages/${params}`);
                        
                        if (response.data.code === 200) {
                            this.images = response.data.data || [];
                            this.updateStats();
                            this.filterImages();
                        } else {
                            this.$message.error(response.data.message || '加载图片失败');
                        }
                    } catch (error) {
                        console.error('加载图片失败:', error);
                        this.$message.error('加载图片失败');
                    } finally {
                        this.loading = false;
                    }
                },
                
                updateStats() {
                    this.stats.total = this.images.length;
                    this.stats.pending = this.images.filter(img => img.auditStatus === 'pending').length;
                    this.stats.approved = this.images.filter(img => img.auditStatus === 'approved').length;
                    this.stats.rejected = this.images.filter(img => img.auditStatus === 'rejected').length;
                },
                
                filterImages() {
                    let filtered = [...this.images];
                    
                    if (this.filterStatus) {
                        filtered = filtered.filter(img => img.auditStatus === this.filterStatus);
                    }
                    
                    this.filteredImages = filtered;
                    this.currentPage = 1;
                },
                
                handleSelectionChange(selection) {
                    this.selectedImages = selection;
                },
                
                toggleSelectAll() {
                    this.$refs.table.toggleAllSelection();
                },
                
                async approveImage(image) {
                    try {
                        this.$set(image, 'processing', true);
                        const response = await axios.post('/whauditimage/', {
                            imageId: image.imageId,
                            action: 'approve'
                        });
                        
                        if (response.data.code === 200) {
                            this.$message.success('审核通过');
                            image.auditStatus = 'approved';
                            this.updateStats();
                        } else {
                            this.$message.error(response.data.message || '审核失败');
                        }
                    } catch (error) {
                        console.error('审核失败:', error);
                        this.$message.error('审核失败');
                    } finally {
                        this.$set(image, 'processing', false);
                    }
                },
                
                async rejectImage(image) {
                    try {
                        this.$set(image, 'processing', true);
                        const response = await axios.post('/whauditimage/', {
                            imageId: image.imageId,
                            action: 'reject'
                        });
                        
                        if (response.data.code === 200) {
                            this.$message.success('已拒绝');
                            image.auditStatus = 'rejected';
                            this.updateStats();
                        } else {
                            this.$message.error(response.data.message || '操作失败');
                        }
                    } catch (error) {
                        console.error('操作失败:', error);
                        this.$message.error('操作失败');
                    } finally {
                        this.$set(image, 'processing', false);
                    }
                },
                
                async batchApprove() {
                    if (!this.selectedImages.length) {
                        this.$message.warning('请先选择要审核的图片');
                        return;
                    }
                    
                    try {
                        this.loading = true;
                        const imageIds = this.selectedImages.map(img => img.imageId);
                        const response = await axios.post('/batchAuditImages/', {
                            imageIds: imageIds,
                            action: 'approve'
                        });
                        
                        if (response.data.code === 200) {
                            this.$message.success(`批量审核通过，共处理 ${imageIds.length} 张图片`);
                            this.loadImages();
                            this.selectedImages = [];
                        } else {
                            this.$message.error(response.data.message || '批量审核失败');
                        }
                    } catch (error) {
                        console.error('批量审核失败:', error);
                        this.$message.error('批量审核失败');
                    } finally {
                        this.loading = false;
                    }
                },
                
                async batchReject() {
                    if (!this.selectedImages.length) {
                        this.$message.warning('请先选择要拒绝的图片');
                        return;
                    }
                    
                    this.$confirm('确定要批量拒绝选中的图片吗？', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(async () => {
                        try {
                            this.loading = true;
                            const imageIds = this.selectedImages.map(img => img.imageId);
                            const response = await axios.post('/batchAuditImages/', {
                                imageIds: imageIds,
                                action: 'reject'
                            });
                            
                            if (response.data.code === 200) {
                                this.$message.success(`批量拒绝成功，共处理 ${imageIds.length} 张图片`);
                                this.loadImages();
                                this.selectedImages = [];
                            } else {
                                this.$message.error(response.data.message || '批量拒绝失败');
                            }
                        } catch (error) {
                            console.error('批量拒绝失败:', error);
                            this.$message.error('批量拒绝失败');
                        } finally {
                            this.loading = false;
                        }
                    });
                },
                
                previewImage(image) {
                    this.currentPreviewImage = image;
                    this.previewDialogVisible = true;
                },
                
                approveImageFromPreview() {
                    this.approveImage(this.currentPreviewImage);
                    this.previewDialogVisible = false;
                },
                
                rejectImageFromPreview() {
                    this.rejectImage(this.currentPreviewImage);
                    this.previewDialogVisible = false;
                },
                
                handleImageError(event) {
                    event.target.src = '/static/images/image-error.png';
                },
                
                getStatusText(status) {
                    const statusMap = {
                        pending: '待审核',
                        approved: '已通过',
                        rejected: '已拒绝'
                    };
                    return statusMap[status] || '未知';
                },
                
                formatDate(dateStr) {
                    if (!dateStr) return '未知';
                    const date = new Date(dateStr);
                    return date.toLocaleString('zh-CN');
                },
                
                handleSizeChange(val) {
                    this.pageSize = val;
                    this.currentPage = 1;
                },
                
                handleCurrentChange(val) {
                    this.currentPage = val;
                }
            }
        });
    </script>
</body>
</html>
