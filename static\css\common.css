/* 知识库管理系统 - 统一样式文件 */

/* 全局样式重置 */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  background-color: #f5f7fa;
  color: #303133;
  line-height: 1.6;
}

/* 主题色彩变量 */
:root {
  --primary-color: #667eea;
  --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --secondary-color: #764ba2;
  --success-color: #67c23a;
  --warning-color: #e6a23c;
  --danger-color: #f56c6c;
  --info-color: #909399;
  
  --text-primary: #303133;
  --text-regular: #606266;
  --text-secondary: #909399;
  --text-placeholder: #c0c4cc;
  
  --border-base: #dcdfe6;
  --border-light: #e4e7ed;
  --border-lighter: #ebeef5;
  --border-extra-light: #f2f6fc;
  
  --background-base: #f5f7fa;
  --background-light: #fafafa;
  --background-white: #ffffff;
  
  --shadow-base: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  --shadow-light: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  --shadow-hover: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* 通用容器样式 */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.main-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 头部工具栏样式 */
.toolbar {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
  padding: 15px 20px;
  background: var(--background-white);
  height: 60px;
  box-shadow: var(--shadow-base);
  position: relative;
  z-index: 1000;
}

/* 导航菜单样式 */
.nav-menu {
  height: calc(100% - 60px);
  margin-top: 60px;
  border-right: none;
  background: var(--background-white);
}

/* 内容区域样式 */
.content-area {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  background: var(--background-base);
}

.card-box {
  padding: 20px;
  height: calc(100vh - 160px);
  overflow-y: auto;
}

/* 卡片样式 */
.card {
  background: var(--background-white);
  border-radius: 8px;
  box-shadow: var(--shadow-light);
  transition: all 0.3s ease;
  overflow: hidden;
}

.card:hover {
  box-shadow: var(--shadow-hover);
  transform: translateY(-2px);
}

.file-card {
  transition: all 0.3s;
  margin-bottom: 20px;
  background: var(--background-white);
  border-radius: 8px;
  padding: 16px;
  border: 1px solid var(--border-lighter);
}

.file-card:hover {
  box-shadow: var(--shadow-base);
  transform: translateY(-5px);
  border-color: var(--primary-color);
}

/* 文件元信息样式 */
.file-meta {
  font-size: 12px;
  color: var(--text-secondary);
  margin: 8px 0;
}

.file-name {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  min-height: 2.4em;
  line-height: 1.2em;
  font-weight: 500;
  color: var(--text-primary);
}

.file-name2 {
  margin-top: 5px;
  font-size: 14px;
  color: var(--text-secondary);
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.2em;
}

/* 状态徽章样式 */
.status-badge {
  position: absolute;
  border-radius: 12px;
  font-size: 12px;
  display: flex;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.9);
  box-shadow: var(--shadow-light);
  padding: 4px 8px;
}

.dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 4px;
}

.dot.red {
  background-color: var(--danger-color);
}

.dot.green {
  background-color: var(--success-color);
}

.status-unreviewed {
  color: var(--danger-color);
}

.status-reviewed {
  color: var(--success-color);
}

/* 预览卡片样式 */
.preview-card-container {
  width: 90%;
  margin: 20px auto 0;
}

.preview-card {
  position: relative;
  border: 1px solid var(--border-base);
  border-radius: 8px;
  transition: all 0.3s ease;
  padding: 16px;
  background-color: #f0f4ff;
}

.preview-card:hover {
  box-shadow: var(--shadow-base);
  transform: translateY(-5px);
  border-color: var(--primary-color);
  background-color: #e8ecff;
}

.preview-card img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  border-radius: 4px;
}

/* 删除图标样式 */
.delete-icon {
  position: absolute;
  bottom: 10px;
  right: 10px;
  cursor: pointer;
  color: var(--danger-color);
  font-size: 18px;
  transition: color 0.3s ease;
}

.delete-icon:hover {
  color: #ff0000;
}

/* 索引样式 */
.index-item {
  margin-bottom: 15px;
}

.default-index, .custom-index {
  border: 1px solid var(--border-base);
  border-radius: 8px;
  padding: 12px;
  background-color: var(--background-base);
  cursor: pointer;
  transition: all 0.3s ease;
}

.custom-index:hover {
  box-shadow: var(--shadow-base);
  border-color: var(--primary-color);
  background-color: #e8ecff;
}

.index-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

/* ID样式 */
.IDstyle {
  position: absolute;
  left: 10px;
  top: 10px;
  font-size: 12px;
  color: white;
  background: rgba(0, 0, 0, 0.6);
  padding: 2px 6px;
  border-radius: 4px;
}

/* 按钮样式 */
.btn-primary {
  background: var(--primary-gradient);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
  background: var(--background-white);
  color: var(--text-primary);
  border: 1px solid var(--border-base);
  padding: 10px 20px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-secondary:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
}

/* 表单样式 */
.form-item {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: var(--text-primary);
}

/* 进度条样式 */
.el-progress {
  margin-top: 20px;
}

/* 自定义标签页样式 */
.custom-tabs .el-tabs__nav-wrap::after {
  display: none;
}

.custom-tabs .el-tab-pane {
  padding-top: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    padding: 0 10px;
  }
  
  .toolbar {
    padding: 10px 15px;
    height: 50px;
  }
  
  .nav-menu {
    margin-top: 50px;
    height: calc(100% - 50px);
  }
  
  .card-box {
    padding: 10px;
    height: calc(100vh - 120px);
  }
  
  .file-card {
    padding: 12px;
  }
  
  .preview-card-container {
    width: 95%;
  }
}

@media (max-width: 480px) {
  .toolbar {
    flex-direction: column;
    height: auto;
    padding: 10px;
  }
  
  .file-card {
    margin-bottom: 15px;
  }
  
  .btn-primary, .btn-secondary {
    padding: 8px 16px;
    font-size: 13px;
  }
}
