<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>对话日志分析</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 20px;
            background-color: #f9f9f9;
        }

        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }

        .table-container {
            overflow-x: auto;
            margin-bottom: 20px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            background-color: #fff;
            border-radius: 8px;
            overflow: hidden;
        }

        th, td {
            border: none;
            padding: 12px 15px;
            text-align: left;
        }

        th {
            background-color: #4CAF50;
            color: white;
            font-weight: bold;
        }

        tr {
            background-color: #fff;
            border-radius: 8px;
            margin-bottom: 10px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s, box-shadow 0.3s;
        }

        tr:nth-child(odd) {
            background-color: #f2f2f2; /* 浅灰色背景 */
        }

        tr:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
        }

        .pagination {
            display: flex;
            justify-content: center;
            margin-top: 20px;
        }

        .pagination button {
            margin: 0 5px;
            padding: 8px 15px;
            cursor: pointer;
            border: none;
            background-color: #4CAF50;
            color: white;
            border-radius: 4px;
            transition: background-color 0.3s;
        }

        .pagination button:hover {
            background-color: #45a049;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: auto;
            background-color: rgba(0, 0, 0, 0.4);
        }

        .modal-content {
            background-color: #fefefe;
            margin: 15% auto;
            padding: 20px;
            border: 1px solid #888;
            width: 80%;
            max-width: 600px;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }

        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover,
        .close:focus {
            color: black;
            text-decoration: none;
            cursor: pointer;
        }

        textarea {
            width: 100%;
            padding: 10px;
            margin-top: 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
            resize: vertical;
        }

        button#saveFix {
            margin-top: 10px;
            padding: 8px 15px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        button#saveFix:hover {
            background-color: #45a049;
        }

        /* 骨架屏样式 */
        .skeleton {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
        }

        @keyframes loading {
            0% {
                background-position: 200% 0;
            }
            100% {
                background-position: -200% 0;
            }
        }

        /* 新增样式 */
        .answer-content {
            max-height: 100px; /* 设置最大高度 */
            overflow: hidden;
            position: relative;
        }

        .answer-content.expanded {
            max-height: none;
        }

        .show-more-btn {
            cursor: pointer;
            color: #4CAF50;
            text-decoration: underline;
        }

        /* 日期过滤控件样式 */
        .date-filter {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 20px;
        }

        .date-filter label {
            margin-right: 10px;
            font-weight: bold;
            color: #333;
        }

        .date-filter input[type="date"] {
            padding: 8px 12px;
            border: 1px solid #ccc;
            border-radius: 4px;
            margin-right: 10px;
            font-size: 14px;
        }

        .date-filter button {
            padding: 8px 15px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .date-filter button:hover {
            background-color: #45a049;
        }

        /* 加载中模态框样式 */
        #loadingModal {
            display: none;
            position: fixed;
            z-index: 2;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: auto;
            background-color: rgba(0, 0, 0, 0.4);
        }

        #loadingModal .modal-content {
            background-color: #fefefe;
            margin: 15% auto;
            padding: 10px;
            border: 1px solid #888;
            width: 20%;
            max-width: 200px;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
            text-align: center;
        }

        #loadingModal h2 {
            color: #aaa;
            font-size: 18px;
            margin: 0;
        }
        .readButton {
            background-color: #ccc;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .readButton.read {
            background-color: #4CAF50;
        }
        /* 详细信息模态框样式 */
        #detailModal {
            display: none;
            position: fixed;
            z-index: 3;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: auto;
            background-color: rgba(0, 0, 0, 0.4);
        }

        #detailModal .modal-content3 {
            background-color: #fefefe;
            margin: 15% auto; /* 调整这里的百分比来改变垂直位置 */
            padding: 20px;
            border: 1px solid #888;
            width: 80%;
            max-width: 1200px;
            height: 80%;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
            position: fixed; /* 确保模态框内容相对于模态框定位 */
            overflow-y: auto; /* 确保内容可以滚动 */
        }

        #detailModal .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        #detailModal .close:hover,
        #detailModal .close:focus {
            color: black;
            text-decoration: none;
            cursor: pointer;
        }

        #detailContent {
            overflow-y: auto;
            height: calc(100% - 100px);
        }
    </style>
</head>
<body>
    <h1>对话日志分析</h1>
    <div class="date-filter">
        <label for="appIdSelect">选择应用:</label>
        <select id="appIdSelect">
            <!-- 应用选项将通过JavaScript动态生成 -->
        </select>
        <div>&nbsp;&nbsp;</div>
        <label for="startDate">开始时间:</label>
        <input type="date" id="startDate">
        <label for="endDate">结束时间:</label>
        <input type="date" id="endDate">
        <label for="isReadSelect">是否阅读:</label>
        <select id="isReadSelect">
            <option value="">全部</option>
            <option value="1">是</option>
            <option value="0">否</option>
        </select>
        <div>&nbsp;&nbsp;</div>
        <label for="isReadSelect">业务块:</label>
        <select id="ywkSelect">
        <option value="">全部</option>
        <option value="zkaccess3.5业务块">zkaccess3.5业务块</option>
        <option value="zktime5.0业务块">zktime5.0业务块</option>
        <option value="zkepos4.0业务块">zkepos4.0业务块</option>
        <option value="熵基互联业务块">熵基互联业务块</option>
        <option value="考勤设备业务块">考勤设备业务块</option>
        <option value="门禁及梯控设备业务块">门禁及梯控设备业务块</option>
        <option value="消费设备业务块">消费设备业务块</option>
        <option value="车行设备业务块">车行设备业务块</option>
        <option value="安检设备业务块">安检设备业务块</option>
        <option value="万傲瑞达V6600业务块">万傲瑞达V6600业务块</option>
        <option value="ecopro业务块">ecopro业务块</option>
        <option value="人行通道设备业务块">人行通道设备业务块</option>
        <option value="人证核验设备业务块">人证核验设备业务块</option>
        <option value="熵基云联业务块">熵基云联业务块</option>
        <option value="智能视频摄像机业务块">智能视频摄像机业务块</option>
    </select>
        <div>&nbsp;&nbsp;</div>
        <button id="filterButton">查询</button>
        <div>&nbsp;&nbsp;</div>
        <button id="exportButton">导出Excel</button> <!-- 新增导出按钮 -->
    </div>
    <div class="table-container">
        <table id="dataTable">
            <thead>
                <tr>
                    <th>编号</th>
                    <th>时间</th>
                    <th>业务块</th>
                    <th>提问</th>
                    <th>回答</th>
                    <th id="likesHeader">评价<span id="likesSortIcon"></span></th>
                    <th>修复内容</th>
                    <th style="width: 100px">操作</th>
                </tr>
            </thead>
            <tbody>
                <!-- 表格数据将通过JavaScript动态生成 -->
            </tbody>
        </table>
    </div>
    <div class="pagination">
        <button id="prevPage">上一页</button>
        <span id="pageInfo"></span>
        <button id="nextPage">下一页</button>
    </div>

    <!-- 模态框 -->
    <div id="modal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>输入修复内容</h2>
            <textarea id="fixContent" rows="4" cols="50"></textarea>
            <br>
            <button id="saveFix">保存</button>
        </div>
    </div>

    <!-- 加载中模态框 -->
    <div id="loadingModal" class="modal">
        <div class="modal-content">
            <h2>加载中...</h2>
        </div>
    </div>
    <!-- 详细信息模态框 -->
    <div id="detailModal" class="modal">
        <div class="modal-content3">
            <span class="close">&times;</span>
            <h2>详细信息</h2>
            <div id="detailContent">
                <p><span id="detailAnswer"></span></p>
            </div>
        </div>
    </div>
    <script src="/media/js/FileSaver.min.js"></script>
    <script src="/media/js/script_log.js"></script>
    <script src="/media/js/xlsx.full.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
</body>
</html>