#!/usr/bin/env python3
"""
文档上传功能测试脚本
用于验证重构后的上传功能是否正常工作
"""

import asyncio
import json
import tempfile
import os
from io import BytesIO
from fastapi import UploadFile
from zhishiku_h5 import (
    validate_upload_file, 
    getFileInfo, 
    build_qa_prompt,
    uploadfiles
)

class MockUploadFile:
    """模拟UploadFile对象用于测试"""
    def __init__(self, filename, content, content_type="application/pdf", size=None):
        self.filename = filename
        self.content_type = content_type
        self.size = size or len(content)
        self._content = content
    
    async def read(self):
        return self._content

async def test_file_validation():
    """测试文件验证功能"""
    print("🧪 测试文件验证功能...")
    
    # 测试正常文件
    normal_file = MockUploadFile("test.pdf", b"test content", size=1024)
    result = await validate_upload_file(normal_file)
    assert result["valid"] == True, "正常文件应该通过验证"
    print("✅ 正常文件验证通过")
    
    # 测试文件过大
    large_file = MockUploadFile("large.pdf", b"test", size=60*1024*1024)  # 60MB
    result = await validate_upload_file(large_file)
    assert result["valid"] == False, "过大文件应该被拒绝"
    print("✅ 大文件验证正确拒绝")
    
    # 测试不支持的文件类型
    invalid_file = MockUploadFile("test.exe", b"test", size=1024)
    result = await validate_upload_file(invalid_file)
    assert result["valid"] == False, "不支持的文件类型应该被拒绝"
    print("✅ 不支持文件类型验证正确拒绝")
    
    # 测试空文件名
    empty_name_file = MockUploadFile("", b"test", size=1024)
    result = await validate_upload_file(empty_name_file)
    assert result["valid"] == False, "空文件名应该被拒绝"
    print("✅ 空文件名验证正确拒绝")

async def test_file_info_extraction():
    """测试文件信息提取功能"""
    print("\n🧪 测试文件信息提取功能...")
    
    test_cases = [
        "xface600使用说明书.pdf",
        "cm500功能参数介绍及使用说明.doc",
        "pro3000用户手册.docx",
        "ZKTime5.0考勤软件操作手册.pdf",
        "万傲瑞达V6600门禁模块使用说明.txt"
    ]
    
    for filename in test_cases:
        try:
            result = await getFileInfo(filename)
            assert result["code"] == 200, f"文件信息提取应该成功: {filename}"
            
            data = result["data"]
            required_fields = ["型号", "名称", "售前售后", "可接入软件"]
            for field in required_fields:
                assert field in data, f"缺少必要字段 {field}: {filename}"
            
            print(f"✅ {filename} -> 型号: {data['型号']}, 名称: {data['名称']}")
        except Exception as e:
            print(f"❌ {filename} 提取失败: {str(e)}")

def test_qa_prompt_building():
    """测试QA提示词构建功能"""
    print("\n🧪 测试QA提示词构建功能...")
    
    dataset_name = "测试知识库"
    dataset_sm = "这是一个测试知识库"
    filename = "test.pdf"
    base_prompt = "这是基础提示词"
    
    prompt = build_qa_prompt(dataset_name, dataset_sm, filename, base_prompt)
    
    assert dataset_name in prompt, "提示词应该包含知识库名称"
    assert dataset_sm in prompt, "提示词应该包含知识库说明"
    assert filename in prompt, "提示词应该包含文件名"
    assert base_prompt in prompt, "提示词应该包含基础提示词"
    
    print("✅ QA提示词构建功能正常")

def test_css_variables():
    """测试CSS变量是否正确定义"""
    print("\n🧪 测试CSS变量定义...")
    
    css_file_path = "static/css/common.css"
    if os.path.exists(css_file_path):
        with open(css_file_path, 'r', encoding='utf-8') as f:
            css_content = f.read()
        
        required_variables = [
            "--primary-color",
            "--primary-gradient", 
            "--success-color",
            "--danger-color",
            "--text-primary",
            "--text-secondary",
            "--background-white",
            "--background-base",
            "--shadow-light",
            "--shadow-hover",
            "--spacing-sm",
            "--spacing-md",
            "--spacing-lg",
            "--radius-sm",
            "--radius-md",
            "--font-size-sm",
            "--font-size-md"
        ]
        
        for var in required_variables:
            assert var in css_content, f"CSS变量 {var} 未定义"
        
        print("✅ CSS变量定义完整")
    else:
        print("❌ CSS文件不存在")

def test_html_structure():
    """测试HTML结构是否正确"""
    print("\n🧪 测试HTML结构...")
    
    # 测试docs.html
    docs_file_path = "templates/docs.html"
    if os.path.exists(docs_file_path):
        with open(docs_file_path, 'r', encoding='utf-8') as f:
            docs_content = f.read()
        
        # 检查关键元素
        required_elements = [
            "upload-dialog",
            "el-steps",
            "upload-area",
            "batch-select-bar",
            "file-checkbox",
            "approve-btn",
            "reject-btn"
        ]
        
        for element in required_elements:
            assert element in docs_content, f"docs.html缺少关键元素: {element}"
        
        print("✅ docs.html结构正确")
    
    # 测试home.html
    home_file_path = "templates/home.html"
    if os.path.exists(home_file_path):
        with open(home_file_path, 'r', encoding='utf-8') as f:
            home_content = f.read()
        
        # 检查是否引入了统一CSS
        assert "/static/css/common.css" in home_content, "home.html未引入统一CSS"
        
        # 检查是否使用了CSS变量
        assert "var(--" in home_content, "home.html未使用CSS变量"
        
        print("✅ home.html结构正确")

async def run_all_tests():
    """运行所有测试"""
    print("🚀 开始运行文档上传功能测试...\n")
    
    try:
        # 运行各项测试
        await test_file_validation()
        await test_file_info_extraction()
        test_qa_prompt_building()
        test_css_variables()
        test_html_structure()
        
        print("\n🎉 所有测试通过！文档上传功能重构成功！")
        
        # 输出功能总结
        print("\n📋 重构功能总结:")
        print("✅ 现代化的分步骤上传界面")
        print("✅ 拖拽上传支持")
        print("✅ 文件信息自动提取")
        print("✅ 批量选择和审核")
        print("✅ 统一的页面风格")
        print("✅ 响应式设计")
        print("✅ 改进的错误处理")
        print("✅ 优化的后端处理逻辑")
        
    except AssertionError as e:
        print(f"\n❌ 测试失败: {str(e)}")
    except Exception as e:
        print(f"\n💥 测试过程中发生错误: {str(e)}")

if __name__ == "__main__":
    asyncio.run(run_all_tests())
