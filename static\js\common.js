/**
 * 知识库管理系统 - 通用JavaScript工具函数
 */

// 通用工具函数
const Utils = {
    /**
     * 格式化日期时间
     * @param {string|Date} dateStr 日期字符串或Date对象
     * @returns {string} 格式化后的日期字符串
     */
    formatDate(dateStr) {
        if (!dateStr) return '未知';
        
        const date = new Date(dateStr);
        if (isNaN(date.getTime())) return '无效日期';
        
        const now = new Date();
        const diff = now - date;
        
        // 小于1分钟
        if (diff < 60000) return '刚刚';
        // 小于1小时
        if (diff < 3600000) return Math.floor(diff / 60000) + '分钟前';
        // 小于1天
        if (diff < 86400000) return Math.floor(diff / 3600000) + '小时前';
        // 小于30天
        if (diff < 2592000000) return Math.floor(diff / 86400000) + '天前';
        
        // 超过30天显示具体日期
        return date.toLocaleDateString('zh-CN');
    },

    /**
     * 格式化文件大小
     * @param {number} bytes 字节数
     * @returns {string} 格式化后的文件大小
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },

    /**
     * 防抖函数
     * @param {Function} func 要防抖的函数
     * @param {number} wait 等待时间（毫秒）
     * @returns {Function} 防抖后的函数
     */
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    /**
     * 节流函数
     * @param {Function} func 要节流的函数
     * @param {number} limit 时间限制（毫秒）
     * @returns {Function} 节流后的函数
     */
    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    },

    /**
     * 深拷贝对象
     * @param {any} obj 要拷贝的对象
     * @returns {any} 拷贝后的对象
     */
    deepClone(obj) {
        if (obj === null || typeof obj !== 'object') return obj;
        if (obj instanceof Date) return new Date(obj.getTime());
        if (obj instanceof Array) return obj.map(item => this.deepClone(item));
        if (typeof obj === 'object') {
            const clonedObj = {};
            for (const key in obj) {
                if (obj.hasOwnProperty(key)) {
                    clonedObj[key] = this.deepClone(obj[key]);
                }
            }
            return clonedObj;
        }
    },

    /**
     * 生成随机ID
     * @param {number} length ID长度
     * @returns {string} 随机ID
     */
    generateId(length = 8) {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        let result = '';
        for (let i = 0; i < length; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
    },

    /**
     * 获取URL参数
     * @param {string} name 参数名
     * @returns {string|null} 参数值
     */
    getUrlParam(name) {
        const urlParams = new URLSearchParams(window.location.search);
        return urlParams.get(name);
    },

    /**
     * 设置URL参数
     * @param {string} name 参数名
     * @param {string} value 参数值
     */
    setUrlParam(name, value) {
        const url = new URL(window.location);
        url.searchParams.set(name, value);
        window.history.pushState({}, '', url);
    },

    /**
     * 验证邮箱格式
     * @param {string} email 邮箱地址
     * @returns {boolean} 是否有效
     */
    validateEmail(email) {
        const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return re.test(email);
    },

    /**
     * 验证手机号格式
     * @param {string} phone 手机号
     * @returns {boolean} 是否有效
     */
    validatePhone(phone) {
        const re = /^1[3-9]\d{9}$/;
        return re.test(phone);
    }
};

// HTTP请求工具
const Http = {
    /**
     * 通用请求方法
     * @param {string} url 请求URL
     * @param {object} options 请求选项
     * @returns {Promise} 请求Promise
     */
    async request(url, options = {}) {
        const defaultOptions = {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
            },
            ...options
        };

        try {
            const response = await fetch(url, defaultOptions);
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const data = await response.json();
            return data;
        } catch (error) {
            console.error('Request failed:', error);
            throw error;
        }
    },

    /**
     * GET请求
     * @param {string} url 请求URL
     * @param {object} params 查询参数
     * @returns {Promise} 请求Promise
     */
    async get(url, params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const fullUrl = queryString ? `${url}?${queryString}` : url;
        return this.request(fullUrl);
    },

    /**
     * POST请求
     * @param {string} url 请求URL
     * @param {object} data 请求数据
     * @returns {Promise} 请求Promise
     */
    async post(url, data = {}) {
        return this.request(url, {
            method: 'POST',
            body: JSON.stringify(data)
        });
    },

    /**
     * PUT请求
     * @param {string} url 请求URL
     * @param {object} data 请求数据
     * @returns {Promise} 请求Promise
     */
    async put(url, data = {}) {
        return this.request(url, {
            method: 'PUT',
            body: JSON.stringify(data)
        });
    },

    /**
     * DELETE请求
     * @param {string} url 请求URL
     * @returns {Promise} 请求Promise
     */
    async delete(url) {
        return this.request(url, {
            method: 'DELETE'
        });
    }
};

// 本地存储工具
const Storage = {
    /**
     * 设置本地存储
     * @param {string} key 键名
     * @param {any} value 值
     */
    set(key, value) {
        try {
            localStorage.setItem(key, JSON.stringify(value));
        } catch (error) {
            console.error('Storage set error:', error);
        }
    },

    /**
     * 获取本地存储
     * @param {string} key 键名
     * @param {any} defaultValue 默认值
     * @returns {any} 存储的值
     */
    get(key, defaultValue = null) {
        try {
            const item = localStorage.getItem(key);
            return item ? JSON.parse(item) : defaultValue;
        } catch (error) {
            console.error('Storage get error:', error);
            return defaultValue;
        }
    },

    /**
     * 删除本地存储
     * @param {string} key 键名
     */
    remove(key) {
        try {
            localStorage.removeItem(key);
        } catch (error) {
            console.error('Storage remove error:', error);
        }
    },

    /**
     * 清空本地存储
     */
    clear() {
        try {
            localStorage.clear();
        } catch (error) {
            console.error('Storage clear error:', error);
        }
    }
};

// 消息提示工具
const Message = {
    /**
     * 成功消息
     * @param {string} message 消息内容
     */
    success(message) {
        if (window.ELEMENT && window.ELEMENT.Message) {
            window.ELEMENT.Message.success(message);
        } else {
            console.log('Success:', message);
        }
    },

    /**
     * 错误消息
     * @param {string} message 消息内容
     */
    error(message) {
        if (window.ELEMENT && window.ELEMENT.Message) {
            window.ELEMENT.Message.error(message);
        } else {
            console.error('Error:', message);
        }
    },

    /**
     * 警告消息
     * @param {string} message 消息内容
     */
    warning(message) {
        if (window.ELEMENT && window.ELEMENT.Message) {
            window.ELEMENT.Message.warning(message);
        } else {
            console.warn('Warning:', message);
        }
    },

    /**
     * 信息消息
     * @param {string} message 消息内容
     */
    info(message) {
        if (window.ELEMENT && window.ELEMENT.Message) {
            window.ELEMENT.Message.info(message);
        } else {
            console.info('Info:', message);
        }
    }
};

// 导出到全局
window.Utils = Utils;
window.Http = Http;
window.Storage = Storage;
window.Message = Message;
