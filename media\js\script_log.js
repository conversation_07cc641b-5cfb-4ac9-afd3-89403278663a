const itemsPerPage = 5; // 每页显示的行数
let currentPage = 1;
let currentRowIndex = -1; // 当前操作的行索引
let data = []; // 存储从接口获取的数据
let chatIds = []; // 存储获取到的 chatId
let question = '';
let questionid = '';
let sortByLikes = false; // 是否按点赞排序
let sortAscending = true; // 排序顺序
let offset = 0; // 页码
var total = 0; // 获取到的总数据

// 获取数据的接口地址
// const appId = "6708e788c6ba48baa62419a5"
const appId = "671ce8508321457ed9b389e0"
const apiUrl = 'http://106.63.8.99:3000/api/core/chat/getHistories';

const appListApiUrl = 'http://106.63.8.99:8009/applist';
const apiUrl1 = 'http://106.63.8.99:8009/getChatLogs/'
const apiUrl2 = 'http://106.63.8.99:8009/getPaginationRecords/'
const apiUrl3 = 'http://106.63.8.99:8009/saveFeedbacks/'

// const appListApiUrl = 'http://192.168.10.51:8009/applist';
// const apiUrl1 = 'http://192.168.10.51:8009/getChatLogs/'
// const apiUrl2 = 'http://192.168.10.51:8009/getPaginationRecords/'
// const apiUrl3 = 'http://192.168.10.51:8009/saveFeedbacks/'

const Cookie = 'zh-CN;   fastgpt_token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiI2NjdiYmRhYTFhNzRlMWI1MTgzMWExYjYiLCJ0ZWFtSWQiOiI2NjdiYmRhYTFhNzRlMWI1MTgzMWExYjkiLCJ0bWJJZCI6IjY2N2JiZGFhMWE3NGUxYjUxODMxYTFiYyIsImlzUm9vdCI6dHJ1ZSwiZXhwIjoxNzMzNzEyMzYxLCJpYXQiOjE3MzMxMDc1NjF9.W27U3ExIANJ0FgA3bGwkJJgLM51bfOkumT1j5Io6xfA'
const headers = {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer fastgpt-e5Lf0kDHo9kXA29cgNVroVWfdNhlw2Csx3ErmS0A0ISfhJyh1EL6eP1HNEqGIX4o',
    'Cookie': 'zh-CN;   fastgpt_token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiI2NjdiYmRhYTFhNzRlMWI1MTgzMWExYjYiLCJ0ZWFtSWQiOiI2NjdiYmRhYTFhNzRlMWI1MTgzMWExYjkiLCJ0bWJJZCI6IjY2N2JiZGFhMWE3NGUxYjUxODMxYTFiYyIsImlzUm9vdCI6dHJ1ZSwiZXhwIjoxNzMzNzEyMzYxLCJpYXQiOjE3MzMxMDc1NjF9.W27U3ExIANJ0FgA3bGwkJJgLM51bfOkumT1j5Io6xfA'
};

// 获取应用列表并填充到下拉选择框
async function fetchAppList() {
    try {
        const response = await fetch(appListApiUrl, {
            method: 'GET',
            headers: headers
        });
        const res = await response.json();
        if (res.code !== 200) {
            throw new Error('获取应用列表失败');
        } else {
            const appList = res.data;
            const appIdSelect = document.getElementById("appIdSelect");
            appList.forEach(app => {
                const option = document.createElement("option");
                option.value = app._id;
                option.textContent = app.name;
                appIdSelect.appendChild(option);
            });
             // 默认选择第一个应用
            if (appList.length > 0) {
                appIdSelect.value = appList[0]._id;
            }
        }
    } catch (error) {
        console.error('获取应用列表失败:', error);
    }
}

// 根据软件请求地址获取数据
async function fetchDataAndDisplayTable2() {
    try {
        showLoadingModal();
        currentPage = 1;
        var pageNum = 1;
        total = 0;
        chatIds = [];
        currentRowIndex = -1;
        data = [];
        const startDate = new Date(document.getElementById("startDate").value);
        const endDate = new Date(document.getElementById("endDate").value);
        const isRead = document.getElementById("isReadSelect").value; // 获取是否阅读的值
        const ywk = document.getElementById("ywkSelect").value; // 获取业务块的值
        if (startDate > endDate) {
            alert("开始时间不能大于结束时间");
            return;
        }

        const selectedAppId = document.getElementById("appIdSelect").value;
        if (!selectedAppId) {
            alert("请选择一个应用");
            return;
        }
        await gettabalData(selectedAppId,startDate.toISOString(),endDate.toISOString(),isRead,ywk);
        displayTable(currentPage);
    } catch (error) {
        console.error('获取数据失败:', error);
    } finally {
        hideLoadingModal();
    }
}

function fomatTime(time) {
    const date = new Date(time + 'Z');
    // 将日期对象转换为 ISO 字符串
    const isoString = date.toISOString();
    // 截取所需的部分并格式化为 yyyy-mm-dd hh:mm:ss
    const formattedDate = isoString.slice(0, 10) + ' ' + isoString.slice(11, 19);
    return formattedDate
}

function gettabalData(appId,stt,ett,isRead,ywk) {
    return new Promise((resolve, reject) => {
        let querydict = {"appId": appId,"dateStart": stt,"dateEnd": ett,"isRead": isRead,"ywk":ywk}
        fetch(apiUrl2, {method: 'POST',headers: {'Content-Type': 'application/json'},body: JSON.stringify(querydict)}).then(response => response.json())
            .then(dataes => {
                let ress = dataes.data.list;
                var chatId = '';
                ress.forEach(item => {
                    try {
                        if (item.obj == 'Human') {
                        	question = '';
                            questionid = '';
                            answer = '';
                            question = item.value[0].text.content;
                            questionid = item._id;
                        } else if (item.obj == 'AI') {
                            let answer = '';
                            for (let i = 0; i < item.value.length; i++) {
                                if (item.value[i].text.content != '') {
                                    answer = item.value[i].text.content;
                                }
                            }
                            // const htmlAnswer = marked.parse(answer);
                            if (ywk != "" && ywk != "全部" && ywk==item.ywk && chatId == item.chatId){
	                            data.push({
	                                id: item._id,
	                                questionid: questionid,
	                                ywk: item.ywk,
	                                question: question,
	                                answer: answer,
	                                likes: item.userGoodFeedback == 'yes' ? 1 : 0,
	                                dislikes: item.userBadFeedback != undefined ? 1 : 0,
	                                fix: item.userBadFeedback || '',
	                                updateTime: item.time, // 假设接口返回了时间戳
	                                isread: item.isread || 0, // 添加 isread 字段
	                                chatId: item.chatId
	                            });
                            
						}
                            if ((ywk == "" || ywk == "全部") && chatId == item.chatId){
	                            data.push({
	                                id: item._id,
	                                questionid: questionid,
	                                ywk: item.ywk,
	                                question: question,
	                                answer: answer,
	                                likes: item.userGoodFeedback == 'yes' ? 1 : 0,
	                                dislikes: item.userBadFeedback != undefined ? 1 : 0,
	                                fix: item.userBadFeedback || '',
	                                updateTime: item.time, // 假设接口返回了时间戳
	                                isread: item.isread || 0, // 添加 isread 字段
	                                chatId: item.chatId
	                            });
						}
                        }
                        chatId = item.chatId
                    } catch (error) {
                        console.error('获取失败:', error);
                    }
                })
                resolve(); // 数据处理完成后，解决 Promise
            })
            .catch(error => {
                reject(error); // 发生错误时，拒绝 Promise
            });
    });
}

function displayTable(page) {
    const start = (page - 1) * itemsPerPage;
    const end = start + itemsPerPage;
    const paginatedData = data.slice(start, end);
    const tableBody = document.querySelector("#dataTable tbody");
    tableBody.innerHTML = "";

    paginatedData.forEach((item, index) => {
        const row = document.createElement("tr");
        row.innerHTML = `
            <td style="width: 5%">${item.id}</td>
            <td>${item.updateTime}</td>
            <td>${item.ywk}</td>
            <td style="width: 10%">${item.question}</td>
            <td style="width: 40%">
                <div class="answer-content">${item.answer}</div>
                <div class="show-more-btn" data-index="${start + index}">显示更多</div>
            </td>
            <td>${item.likes ? '赞' : item.dislikes ? '踩' : ''}</td>
            <td>${item.fix}</td>
            <td>
                <button class="editButton" data-index="${start + index}">操作</button>
                <button class="readButton" data-index="${start + index}" style="background-color: ${item.isread ? '#4CAF50' : '#ccc'}">${item.isread ? '已阅' : '未阅'}</button>
            </td>
        `;
        tableBody.appendChild(row);
    });

    document.getElementById("pageInfo").textContent = `第 ${page} 页，共 ${Math.ceil(data.length / itemsPerPage)} 页`;

    // 添加操作按钮的事件监听器
    const editButtons = document.querySelectorAll(".editButton");
    editButtons.forEach(button => {
        button.addEventListener("click", () => {
            currentRowIndex = parseInt(button.getAttribute("data-index"));
            document.getElementById("modal").style.display = "block";
        });
    });

    // 添加已阅按钮的事件监听器
    const readButtons = document.querySelectorAll(".readButton");
    readButtons.forEach(button => {
        button.addEventListener("click", () => {
            currentRowIndex = parseInt(button.getAttribute("data-index"));
            showLoadingModal();
            let requestBody = {"id": data[currentRowIndex].id,
                "type": 1,
                "questionid": data[currentRowIndex].questionid,
                "chatId": data[currentRowIndex].chatId,
            };
            fetch(apiUrl3, {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify(requestBody)
            }).then(response => response.json())
                .then(dataes => {
                    if (dataes.code == 200) {
                        data[currentRowIndex].isread = 1;
                        displayTable(currentPage);
                        // alert('已阅成功');
                    } else {
                        alert('操作失败');
                    }
                })
                .finally(() => {
                    hideLoadingModal();
                });
        });
    });


    // 添加显示更多按钮的事件监听器
    // const showMoreButtons = document.querySelectorAll(".show-more-btn");
    // showMoreButtons.forEach(button => {
    //     button.addEventListener("click", () => {
    //         const index = parseInt(button.getAttribute("data-index"));
    //         const answerContent = button.previousElementSibling;
    //         if (answerContent.classList.contains("expanded")) {
    //             answerContent.classList.remove("expanded");
    //             button.textContent = "显示更多";
    //         } else {
    //             answerContent.classList.add("expanded");
    //             button.textContent = "收起";
    //         }
    //     });
    // });
    // 添加显示更多按钮的事件监听器
    const showMoreButtons = document.querySelectorAll(".show-more-btn");
    showMoreButtons.forEach(button => {
        button.addEventListener("click", () => {
            const index = parseInt(button.getAttribute("data-index"));
            const item = data[index];
            document.getElementById("detailModal").querySelector("h2").innerText = item.question
            document.getElementById("detailAnswer").innerHTML = marked.parse(item.answer);
            showDetailModal(); // 调用显示模态框的函数
        });
    });

    // 模态框关闭按钮
    document.querySelector("#detailModal .close").addEventListener("click", () => {
        document.getElementById("detailModal").style.display = "none";
    });
}

// 显示模态框时禁止页面滚动
function showDetailModal() {
    const modal = document.getElementById("detailModal");
    const modalContent = modal.querySelector(".modal-content3");

    // 显示模态框
    modal.style.display = "block";
    document.body.style.overflow = "hidden"; // 禁止页面滚动

    // 动态计算并设置模态框的位置
    const windowHeight = window.innerHeight;
    const modalHeight = modalContent.offsetHeight;
    const topPosition = (windowHeight - modalHeight) / 2-300;

    modalContent.style.position = "absolute";
    modalContent.style.top = `${topPosition}px`;
    modalContent.style.left = "50%";
    modalContent.style.transform = "translateX(-50%)";
}

// 隐藏模态框时恢复页面滚动
function hideDetailModal() {
    document.getElementById("detailModal").style.display = "none";
    document.body.style.overflow = "auto"; // 恢复页面滚动
}

// 绑定事件
document.querySelector("#detailModal .close").addEventListener("click", hideDetailModal);

document.getElementById("prevPage").addEventListener("click", () => {
    if (currentPage > 1) {
        currentPage--;
        displayTable(currentPage);
    }
});

document.getElementById("nextPage").addEventListener("click", () => {
    if (currentPage < Math.ceil(data.length / itemsPerPage)) {
        currentPage++;
        displayTable(currentPage);
    }
});

// 模态框关闭按钮
document.querySelector(".close").addEventListener("click", () => {
    document.getElementById("modal").style.display = "none";
});

// 保存修复内容
document.getElementById("saveFix").addEventListener("click", () => {
    const fixContent = document.getElementById("fixContent").value;
    if (currentRowIndex !== -1) {
        data[currentRowIndex].fix = fixContent;
        displayTable(currentPage);
        let requestBody = {"id":data[currentRowIndex].id,"fix":fixContent}
        fetch(apiUrl3, {method: 'POST',headers: {'Content-Type': 'application/json'},body: JSON.stringify(requestBody)}).then(response => response.json())
            .then(dataes => {
                if (dataes.code == 200) {
                    alert('保存成功');
                    let vvv = document.getElementById("fixContent");
                    vvv.value = '';
                } else if (dataes.code == 520) {
                    alert('重复提交');
                }else {
                    alert('保存失败');
                }
            })

        console.log('data[currentRowIndex]------------=', data[currentRowIndex]);
        // const jsonData = JSON.stringify(data, null, 2);
        // const blob = new Blob([jsonData], { type: 'application/json' });
        // saveAs(blob, 'data.json');
    }
    document.getElementById("modal").style.display = "none";
});

// 排序功能
document.getElementById("likesHeader").addEventListener("click", () => {
    sortByLikes = true;
    sortAscending = !sortAscending;
    sortData();
    currentPage = 1;
    displayTable(currentPage);
});

function sortData() {
    if (sortByLikes) {
        data.sort((a, b) => {
            if (sortAscending) {
                return a.dislikes - b.dislikes;
            } else {
                return b.dislikes - a.dislikes;
            }
        });
        document.getElementById("likesSortIcon").textContent = sortAscending ? "▲" : "▼";
    } else {
        document.getElementById("likesSortIcon").textContent = "";
    }
}

// 骨架屏效果
function showSkeleton() {
    const tableBody = document.querySelector("#dataTable tbody");
    tableBody.innerHTML = "";
    for (let i = 0; i < itemsPerPage; i++) {
        const row = document.createElement("tr");
        row.innerHTML = `
            <td class="skeleton" style="width: 5%"></td>
            <td class="skeleton"></td>
            <td class="skeleton"></td>
            <td class="skeleton" style="width: 10%"></td>
            <td class="skeleton" style="width: 60%"></td>
            <td class="skeleton"></td>
            <td class="skeleton"></td>
            <td class="skeleton"></td>
        `;
        tableBody.appendChild(row);
    }
}

// 显示骨架屏
showSkeleton();

// 日期过滤功能
document.getElementById("filterButton").addEventListener("click", () => {
    fetchDataAndDisplayTable2()
});

document.addEventListener("DOMContentLoaded", () => {
    // 获取当前日期
    const today = new Date();
    const threeDaysAgo = new Date();
    threeDaysAgo.setDate(today.getDate() - 3);

    // 格式化日期为 YYYY-MM-DD
    const formatDate = (date) => {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    };

    // 设置日期控件的默认值
    document.getElementById("startDate").value = formatDate(threeDaysAgo);
    document.getElementById("endDate").value = formatDate(today);

    // 初始化表格数据
    // 初始化表格数据
    fetchAppList().then(() => {
        fetchDataAndDisplayTable2();
    });
});

// 显示加载中模态框
function showLoadingModal() {
    document.getElementById("loadingModal").style.display = "block";
}

// 隐藏加载中模态框
function hideLoadingModal() {
    document.getElementById("loadingModal").style.display = "none";
}

// 导出Excel功能
document.getElementById("exportButton").addEventListener("click", () => {
    if (data.length === 0) {
        alert("没有数据可以导出");
        return;
    }
    // 创建一个新的工作簿
    const workbook = XLSX.utils.book_new();

    // 创建一个工作表
    const worksheet = XLSX.utils.json_to_sheet(data);

    // 将工作表添加到工作簿
    XLSX.utils.book_append_sheet(workbook, worksheet, "FastGPT日志");

    // 生成Excel文件并下载
    const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
    const blob = new Blob([excelBuffer], { type: 'application/octet-stream' });
    saveAs(blob, 'FastGPT日志.xlsx');
});