import os
import re
import base64
from pymongo import MongoClient
from bson.objectid import ObjectId
from fastapi.responses import HTMLResponse
from fastapi import <PERSON><PERSON>P<PERSON><PERSON><PERSON>,Request, HTTPException
from config import dataids_list,init_monogdb,apiIP

pattern = r'!\[.*?\]\(/api/system/img/[a-f0-9]{24}\.(?:png|jpeg|jpg)\)'
global_datasets_dic = {}
global_datasets_collections_dic = {}

def get_global_data():
    global global_datasets_dic
    global global_datasets_collections_dic
    db = init_monogdb()
    datasets = db['datasets'].find({"_id": {"$in": dataids_list}})
    global_datasets_dic = {str(d["_id"]):d["name"] for d in datasets}

    collections = db['dataset_collections'].find({"datasetId": {"$in": dataids_list},"type":"file"})
    global_datasets_collections_dic = {str(d["_id"]):d["name"] for d in collections}


async def getcollectionsList(datasetId: str):
    try:
        if datasetId:
            datasetId = [ObjectId(datasetId)]
        else:
            datasetId = [dataids_list[0]]
        db = init_monogdb()
        dataset_collections = db['dataset_collections'].find({"datasetId": {"$in": datasetId},"type":"file"})
        datasets_dic = [{"id":str(d["_id"]),"name":d["name"]} for d in dataset_collections]
        return {"code":200,"data":datasets_dic}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

def get_image_data(q,tag=0):
    match_dict = {}
    picid_list = []
    if '/api/system/img/' in q:
        matches = re.findall(pattern, q)
        for matche in matches:
            picid = matche.split('img/')[1].split('.')[0]
            if tag == 0:
                new_matche = matche.split('(')[1].split(')')[0]
                updateimage = f'<img src="" style="width: 100px; height: auto;" class="uploadedImage">'
                match_dict[matche] = {"src":f'<img src="http://{apiIP}:3000{new_matche}" loading="lazy" referrerpolicy="no-referrer"/>',"picid":picid,"updateimage":updateimage}
            picid_list.append(ObjectId(picid))
    if tag == 0:
        return match_dict,picid_list
    else:
        return picid_list


async def getDatasetsDatas_images(dateStart: str, dateEnd: str, collectionId: str,offset:int = 0,limit:int = 10,isaudit:int = 0):
    try:
        global global_datasets_dic
        global global_datasets_collections_dic
        if not global_datasets_dic or not global_datasets_collections_dic:
            get_global_data()
        db = init_monogdb()
        if isaudit == 0:
            query = {"collectionId": ObjectId(collectionId)}
            dataset_datas = db['dataset_datas'].find(query).skip(offset).limit(limit)
            total = db['dataset_datas'].count_documents(query)
        else:
            # 查询审核人的未审核的数据
            imageres = db["images"].distinct("metadata.datasets_dataid",{ "metadata.audit": "0" })
            ddids = [ObjectId(i) for i in imageres[offset:offset+limit]]
            dataset_datas = db['dataset_datas'].find({"_id": {"$in": ddids}})
            total = len(imageres)

        dataset_list = []
        for dd in dataset_datas:
            q = dd["q"]
            images_dict,picid_list = get_image_data(dd["q"])
            for k,v in images_dict.items():
                q = q.replace(k, v["src"])
            uploadimages = db['images'].find({"_id": {"$in": picid_list}})
            uploadimages_str = ""
            audit = 0
            images_dict_keys = images_dict.keys()
            for upimage in uploadimages:
                metadata = upimage.get("metadata",{})
                if not metadata:
                    continue
                if metadata.get("audit","") == "1":
                    audit = 1
                uploadTag = upimage["metadata"].get("uploadTag","")
                if uploadTag in ["0", "1"]:
                    mime = metadata.get('mime','')
                    for kkkey in images_dict_keys:
                        if str(upimage["_id"]) in kkkey:
                            kkk = kkkey
                            break
                    if uploadTag == "0":
                        imagedata = upimage["metadata"].get("imagedata","")
                        imagedata_base64 =""
                        if imagedata:
                            imagedata_base64 = base64.b64encode(imagedata).decode('utf-8')
                        uploadimages_str += f'<img src="data:{mime};base64,{imagedata_base64}" loading="lazy" referrerpolicy="no-referrer" width="100" height="100"/>'
                        # 操作里面加一个未审核的图片
                        updateimage = f'<img src="data:{mime};base64,{imagedata_base64}" style="width: 100px; height: auto;" class="uploadedImage">'
                        images_dict[kkk]["updateimage"] = updateimage
                    else:
                        images_dict[kkk]["updateimage"] = f'<img src="" style="width: 100px; height: auto;" class="uploadedImage" alt="已标记删除">'
            # 已审核数据 不需要显示待更新图片
            if audit == 1:
                uploadimages_str = ""
            updateTime = dd["updateTime"].strftime("%Y-%m-%d %H:%M:%S")
            tem_dic = {"id":str(dd["_id"]),"datasetName":global_datasets_dic[str(dd["datasetId"])],
                       "collectionName":global_datasets_collections_dic[str(dd["collectionId"])],
                       "q":q,"updateTime":updateTime,"images_dict":images_dict,
                       "uploadimages_str":uploadimages_str,"audit":audit}
            dataset_list.append(tem_dic)
        dataset_datas = {"code":200,"data":{"total":total,"data":dataset_list}}
        return dataset_datas
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

async def uploadimage(body: dict):
    try:
        db = init_monogdb()
        type = body.get("type","0")
        if type in ["4","3"]:
            #直接对知识片段进行操作  4 删除片段里面的图片   3 还原片段里面的操作
            picidobj = [ObjectId(bb) for bb in body["picid"]]
            picobjs = db['images'].find({"_id": {"$in": picidobj}})
            for picobj in picobjs:
                metadata = picobj["metadata"]
                if type == "4":
                    if picobj.get("metadata",{}).get("uploadTag","") != "1":
                        metadata["uploadTag"] = "1"
                        metadata["audit"] = "0"
                        metadata["datasets_dataid"] = body["datasets_dataid"]
                else:
                    metadata = {"mime":metadata["mime"], "relatedId":metadata['relatedId']}
                update = {"$set": {"metadata": metadata}}
                # 执行查询并排序
                dataset_datas = db['images'].update_one({"_id": picobj["_id"]}, update)
            dataset_datas_new = {"code":200}
        else:
            query = {"_id": ObjectId(body["picid"])}
            metadata = list(db["images"].find(query))[0].get("metadata",{})
            if type == "0":
                imagedata =  base64.b64decode(body["fix"].split(',')[1])
                # uploadTag 0 是更新图片  1是删除图片
                metadata["uploadTag"] = "0"
                metadata["imagedata"] = imagedata
                metadata["audit"] = "0"
                metadata["datasets_dataid"] = body["datasets_dataid"]
            elif type == "1":
                # uploadTag 0 是更新图片  1是删除图片
                metadata["uploadTag"] = "1"
                metadata["datasets_dataid"] = body["datasets_dataid"]
                metadata["audit"] = "0"
            else:
                # 2 是清除提交内容
                metadata = {"mime":metadata["mime"], "relatedId":metadata['relatedId']}
            update = {"$set": {"metadata": metadata}}
            # 执行查询并排序
            dataset_datas = db['images'].update_one(query, update)
            if dataset_datas.modified_count > 0:
                dataset_datas_new = {"code":200}
            else:
                dataset_datas_new = {"code":520}
        return dataset_datas_new
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

async def whauditimage(body: dict):
    try:
        delcount = 0
        updatecount = 0
        db = init_monogdb()
        type = int(body.get("tag","0"))
        all_picids = []
        if type == 0: # 更新一个片段图片
            query = {"_id": ObjectId(body["id"])}
            datasetdatas = list(db["dataset_datas"].find(query))
            for dd in datasetdatas:
                all_picids.extend(get_image_data(dd["q"],1))
            # imagesdatas = list(db["images"].find({"_id":{"$in": all_picids}}))
            # if not imagesdatas:
            imagesdatas = list(db["images"].find({"metadata.datasets_dataid":body["id"],"metadata.audit":"0"}))

            if not imagesdatas and not all_picids:
                del_res = db['images'].delete_one({"metadata.datasets_dataid":body["id"]})
                if del_res.deleted_count > 0:
                    delcount += 1
        else:# 一键审核
            imagesdatas = list(db["images"].find({"metadata.audit":"0"}))

        for imdata in imagesdatas:
            try:
                metadata = imdata["metadata"]
                # uploadTag 0 是更新图片  1是删除图片
                if metadata["uploadTag"] == "0":
                    imagedata = metadata.get("imagedata", imdata["binary"])
                    metadata["audit"] = "1"
                    update = {"$set": {"metadata": metadata, "binary": imagedata}}
                    update_res = db['images'].update_one({"_id": imdata["_id"]}, update)
                    if update_res.modified_count > 0:
                        updatecount += 1
                else:
                    mime = metadata.get("mime","")
                    datasets_dataid = metadata["datasets_dataid"]
                    image_pattern = re.compile(r'!\[.*?\]\(/api/system/img/' + str(imdata["_id"]) + r'\.' + mime.replace('image/','') + r'\)')
                    # delurl = '![](/api/system/img/'+str(imdata["_id"]) + '.'+mime.replace('image/','')+')'
                    deldatas = db["dataset_datas"].find({"_id": ObjectId(datasets_dataid)})
                    update = {}
                    for delda in deldatas:
                        # q = delda["q"].replace(delurl, '')
                        q = image_pattern.sub('', delda["q"])
                        update = {"$set": {"q": q}}
                    if not update:
                        continue
                    update_res = db['dataset_datas'].update_one({"_id": ObjectId(datasets_dataid)}, update)
                    # if update_res.modified_count > 0:
                    del_res = db['images'].delete_one({"_id": imdata["_id"]})
                    if del_res.deleted_count > 0:
                        delcount += 1
            except Exception as e:
                print('----------------ww-=',str(e))        
        return {"code":200,"msg":f"更新{updatecount}条数据，删除{delcount}条数据"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


async def h5_images(request: Request):
     # 读取 HTML 文件内容
    with open(os.path.join("templates", "images.html"), "r", encoding="utf-8") as file:
        html_content = file.read()
    return HTMLResponse(content=html_content, status_code=200)


async def h5_images_audit_wh(request: Request):
     # 读取 HTML 文件内容
    with open(os.path.join("templates", "audit_images.html"), "r", encoding="utf-8") as file:
        html_content = file.read()
    return HTMLResponse(content=html_content, status_code=200)