<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <!-- 引入样式 -->
  <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
  <style>
    /* 自定义样式 */
    .main-container {
      height: 100vh;
    }
    .nav-menu {
      height: calc(100% - 60px); /* 留出顶部空间 */
      margin-top: 60px; /* 下移导航栏 */
      border-right: none;
    }
    .toolbar {
      display: flex;
      justify-content: center; /* 居中对齐 */
      align-items: center;
      gap: 20px;
      padding: 15px 20px;
      background: white;
      height: 60px;
      box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
      position: relative;
      z-index: 1;
    }
    .card-box {
      padding: 20px;
      height: calc(100vh - 160px);
      overflow-y: auto;
    }
    .file-card {
      transition: all 0.3s;
      margin-bottom: 20px;
    }
    .file-card:hover {
      box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
      transform: translateY(-5px);
    }
    .file-meta {
      font-size: 12px;
      color: #909399;
      margin: 8px 0;
    }
    /* 弹窗进度条样式 */
    .el-progress {
      margin-top: 20px;
    }

    /* 表单间距调整 */
    .el-form-item {
      margin-bottom: 20px;
    }
      /* 状态徽章 */
    .status-badge {
      position: absolute;
      border-radius: 12px;
      font-size: 12px;
      display: flex;
      align-items: center;
      background-color: rgba(255, 255, 255, 0.9);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    /* 红点和绿点 */
    .dot {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      margin-right: 4px;
    }
    .red {
      background-color: #ff4d4f; /* 红色 */
    }
    .green {
      background-color: #52c41a; /* 绿色 */
    }
    /* 未审核状态 */
    .status-unreviewed {
      color: #ff4d4f; /* 红色文字 */
    }
    /* 已审核状态 */
    .status-reviewed {
      color: #52c41a; /* 绿色文字 */
    }
    .file-name {
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
      min-height: 2.4em; /* 设置最小高度为两行 */
      line-height: 1.2em; /* 设置行高 */
    }
    .file-name2 {
    	 margin-top: 5px;
    	 font-size:14px;
    	 color:#919397;
      display: -webkit-box;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
      line-height: 1.2em; /* 设置行高 */
    }
    .preview-card-container {
      width: 90%; /* 卡片宽度为预览页面的60% */
      margin: 0 auto; /* 居中显示 */
      margin-top: 20px; /* 卡片之间的间距 */
    }

    .preview-card {
      position: relative;
      border: 1px solid #c6c9ce; /* 添加边框 */
      border-radius: 5px; /* 圆角 */
      transition: all 0.5s;
      padding: 10px; /* 内边距 */
      background-color: #f0f4ff;  /* 背景色 */
    }

    .preview-card:hover {
      box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
      transform: translateY(-5px);
      border: #3370FF 1px solid; /* 边框颜色 */
      background-color: #d2d5e1fd;  /* 背景色 */
    }
    .preview-card img {
        width: 100%;
        height: 100%;
        object-fit: contain; /* 保持比例，完整显示图片 */
    }

    .delete-icon {
      position: absolute;
      bottom: 10px;
      right: 10px;
      cursor: pointer;
      color: #F56C6C;
      font-size: 18px;
    }

    .delete-icon:hover {
      color: #ff0000;
    }
    /* 预览模态框样式 */
    .preview-modal {
      .el-dialog__body {
        padding: 20px;
      }
      .el-tabs__content {
        padding: 20px;
      }
      .el-textarea {
        width: 100%;
      }
    }
    /* 数据索引样式 */
.index-item {
  margin-bottom: 15px;
}

.default-index, .custom-index {
  border: 1px solid #dcdfe6; /* 添加边框 */
  border-radius: 4px; /* 圆角 */
  padding: 10px; /* 内边距 */
  background-color: #f5f7fa; /* 背景色 */
  cursor: pointer; /* 鼠标悬停时显示手型 */
}

.index-content {
  display: flex;
  flex-direction: column;
  gap: 5px; /* 两行数据之间的间距 */
}

.custom-index .el-input {
  width: 100%;
}
.custom-index:hover {
      box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
      border: #3370FF 1px solid; /* 边框颜色 */
      background-color: #d2d5e1fd;  /* 背景色 */
  }
/* 去掉 el-tabs 的底部横线 */
.custom-tabs .el-tabs__nav-wrap::after {
  display: none;
}

/* 调整新增索引按钮的样式 */
.custom-tabs .el-tab-pane {
  padding-top: 10px; /* 可以根据需要调整 */
}
.IDstyle {
  position: absolute;
  left: 10px;
  top: 10px;
  font-size: 12px;
  color: white;
}
.IDstyle:hover {
  color: #3370FF;
  display: block; /* 鼠标悬停时显示ID样式 */
}

/* 审核对话框样式 */
.audit-dialog {
  display: flex;
  flex-direction: column;
  max-height: 70vh;
}

.audit-dialog .el-dialog__body {
  padding: 20px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  flex: 1;
}

.audit-dialog-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.same-files-table-container {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

/* 表格容器，确保内容可滚动 */
.same-files-table-container .el-table {
  flex: 1;
  overflow: auto;
}

/* 底部按钮固定 */
.audit-dialog-footer {
  padding: 10px 20px;
  border-top: 1px solid #ebeef5;
  text-align: right;
}

/* 调整表头固定 */
.same-files-table-container .el-table__header-wrapper {
  position: sticky;
  top: 0;
  z-index: 1;
  background: white;
}
  </style>
</head>
<body>
  <div id="app">
    <el-container class="main-container">
      <!-- 左侧导航 -->
      <el-aside width="240px" style="background:#f5f7fa">
        <el-menu
          class="nav-menu"
          :default-active="activeMenu"
          background-color="#f5f7fa"
          text-color="#606266"
          active-text-color="#409EFF"
          @select="handleMenuSelect">
          <el-menu-item index="2">
            <i class="el-icon-folder-opened"></i>
            <span>文档库</span>
          </el-menu-item>
          <el-menu-item index="1">
            <i class="el-icon-message"></i>
            <span>对话日志</span>
          </el-menu-item>
          <el-menu-item index="3">
            <i class="el-icon-data-analysis"></i>
            <span>知识库片段修改</span>
          </el-menu-item>
          <el-menu-item index="4" v-if="sfilesh">
            <i class="el-icon-data-analysis"></i>
            <span>知识库片段审核</span>
          </el-menu-item>
        </el-menu>
      </el-aside>

      <!-- 右侧内容 el-icon-data-analysis-->
      <el-main style="padding:0">
        <!-- 顶部工具栏 -->
        <div class="toolbar">
          <el-input
            v-model="searchKey"
            placeholder="搜索文件"
            prefix-icon="el-icon-search"
            style="width: 240px"
            clearable
            @input="handleSearch">
          </el-input>

          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :picker-options="pickerOptions">
          </el-date-picker>

          <!-- 新增的知识库下拉选择框 -->
          <el-select
            v-model="selectedLibrary"
            placeholder="选择知识库"
            clearable
            style="width: 160px; margin-right: 10px;">
            <el-option
              v-for="kb in libraries"
              :key="kb.id"
              :label="kb.name"
              :value="kb.id">
            </el-option>
          </el-select>

          <el-select
            v-model="filterStatus"
            placeholder="是否审核"
            clearable
            style="width: 120px; margin-right: 10px;">
            <el-option label="未审核" value="0"></el-option>
            <el-option label="已审核" value="1"></el-option>
          </el-select>

          <el-button
            type="primary"
            icon="el-icon-search"
            @click="handleQuery">
            查询
          </el-button>
          <el-button
              type="primary"
              icon="el-icon-upload"
              @click="handleOpenImport">
              导入数据
          </el-button>
          <!-- 登录按钮 -->

          <el-button v-if="!sfilesh"
            type="info"
            icon="el-icon-user"
            @click="handleLoginClick('logon')"
            style="margin-left: auto;">
            登录
          </el-button>
          <el-button v-else
            type="primary"
            icon="el-icon-user"
            @click="handleLoginClick('exit')"
            style="margin-left: auto;">
            退出
          </el-button>
        </div>
        <!-- 卡片展示区 -->
        <div class="card-box">
          <el-row :gutter="20" v-if="activeMenu==2">
            <el-col
              v-for="(file, index) in filteredFiles"
              :key="index"
              :xs="12" :sm="8" :md="6" :lg="6" :xl="4">
              <el-card class="file-card">
                <div class="file-header" style="font-size: 10px;display:none;">
                  {{file.datasetId}} {{file.id}}
                </div>
                <div style="position: relative;">
                  <!-- 状态显示 -->
                  <div v-if="file.audit == 0" class="status-badge status-unreviewed">
                    <span class="dot red"></span>未审核
                  </div>
                  <div v-else class="status-badge status-reviewed">
                    <span class="dot green"></span>已审核
                  </div>
                </div>
                <div style="text-align:center">
                  <i class="el-icon-document" style="font-size:36px;color:#409EFF"></i>
                  <el-tooltip class="item" effect="dark" :content="file.name" placement="top">
                    <div class="file-name">{{ file.name }}</div>
                  </el-tooltip>
                  <div class="file-name2">{{file.datasetname}}</div>
                  <div class="file-meta">
                    <div>{{ file.size | formatSize }}</div>
                    <div>{{ file.time | formatTime }}</div>
                  </div>
                  <div>
                    <el-button type="text" @click="previewFile(file)">预览</el-button>
                    <el-button type="text" @click="auditFile(file)" v-if="sfilesh && file.audit == 0">审核</el-button>
                    <el-button type="text" style="color:#F56C6C" @click="deleteFile(file)" v-if="file.audit == 0">删除</el-button>
                  </div>
                </div>
              </el-card>
            </el-col>
          </el-row>

          <!-- 骨架屏 -->
          <el-skeleton :loading="loading" animated>
            <!-- 骨架屏内容 -->
          </el-skeleton>
        </div>
      </el-main>
    </el-container>

    <!-- 删除确认对话框 -->
    <el-dialog
      title="提示"
      :visible.sync="deleteDialogVisible"
      width="30%">
      <span>确认删除该文件？</span>
      <span slot="footer">
        <el-button @click="deleteDialogVisible = false">取 消</el-button>
        <el-button type="danger" @click="confirmDelete">确 定</el-button>
      </span>
    </el-dialog>

    <el-dialog
        title="数据导入"
        :visible.sync="importDialogVisible"
        width="500px">
        <el-form label-width="80px">
            <el-form-item label="知识库">
            <el-select
                v-model="selectedLibrary"
                placeholder="请选择知识库"
                style="width:100%">
                <el-option
                v-for="item in libraries"
                :key="item.id"
                :label="item.name"
                :value="item.id">
                </el-option>
            </el-select>
            </el-form-item>

            <!-- 新增处理方式 -->
            <el-form-item label="处理方式">
                <el-radio-group v-model="processingMethod"  @input="handleInput">
                    <el-radio label="chunk" border>直接分段</el-radio>
                    <el-radio label="qa" border>问答拆分</el-radio>
                </el-radio-group>
            </el-form-item>

            <!-- 新增处理参数 -->
            <el-form-item label="处理参数">
                <el-radio-group v-model="processingParam" vertical>
                    <el-radio label="auto" border>自动</el-radio>
                    <el-radio label="custom" border>自定义</el-radio>
                </el-radio-group>

                <div v-if="processingParam === 'custom'" style="margin-top: 20px">
                    <el-form-item
                        label="分块长度"
                        :rules="[{ required: true, message: '请输入分块长度', trigger: 'blur' },
                        { type: 'number', min: processingMethod === 'chunk' ? 100 : 4000,
                                      max: processingMethod === 'chunk' ? 1800 : 16000,
                                      message: processingMethod === 'chunk' ? '长度范围100-1800' : '长度范围4000-16000',
                                      trigger: 'blur' }]">
                        <el-input
                            v-model.number="chunkLength"
                            type="number"
                            :placeholder="processingMethod === 'chunk' ? '100-1800' : '4000-16000'">
                        </el-input>
                    </el-form-item>

                    <el-form-item label="分隔符号" style="margin-top: 20px">
                        <el-input v-model="customSeparator" placeholder="例如：\n\n"></el-input>
                    </el-form-item>
                </div>
            </el-form-item>

            <el-form-item label="文件">
              <el-upload
                action="/uploadfiles/"
                accept=".docx, .doc"
                :http-request="customUpload"
                :show-file-list="true"
                :file-list="selectedFiles"
                :before-upload="beforeUpload"
                :on-progress="handleProgress"
                :on-preview="handlePreview"
                :on-remove="handleRemove"
                :on-change="handleFileChange"
                :auto-upload="false"
                multiple>
                <el-button slot="trigger" type="primary">选择文件</el-button>
                <el-button style="margin-left: 10px;" size="primary" type="success" @click="uploadAllFiles">上传到服务器</el-button>
                <!-- <div slot="tip" class="el-upload__tip">只能上传word文档</div> -->
              </el-upload>
            </el-form-item>
            <el-progress
            :percentage="uploadProgress"
            v-show="uploadProgress > 0"
            :status="uploadStatus || 'success'"/>
        </el-form>
    </el-dialog>

    <!-- 登录对话框 -->
    <el-dialog
      title="登录"
      :visible.sync="loginDialogVisible"
      width="30%">
      <el-form :model="loginForm" label-width="80px">
        <el-form-item label="账号">
          <el-input v-model="loginForm.username" placeholder="请输入账号"></el-input>
        </el-form-item>
        <el-form-item label="密码">
          <el-input v-model="loginForm.password" type="password" placeholder="请输入密码"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer">
        <el-button @click="loginDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleLogin">登 录</el-button>
      </span>
    </el-dialog>

    <el-dialog
      title="审核文件"
      :visible.sync="auditDialogVisible"
      width="50%"
      class="audit-dialog">
      <div class="audit-dialog-content">
        <el-form :model="auditForm" label-width="80px">
          <!-- 新增同名文件列表 -->
          <el-form-item label="同名文件" v-if="sameNameFiles.length > 0">
            <div class="same-files-table-container">
              <el-table
                :data="sameNameFiles"
                style="width: 100%"
                height="100%">
                <el-table-column prop="name" label="文件名" width="180"></el-table-column>
                <el-table-column prop="time" label="上传时间" width="180" :formatter="formatTime"></el-table-column>
                <el-table-column label="操作" width="180">
                  <template slot-scope="scope">
                    <el-button size="mini" @click="previewSameNameFile(scope.row)">预览</el-button>
                    <el-button size="mini" type="danger" @click="deleteSameNameFile(scope.row)">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-form-item>

          <el-form-item label="审核结果">
            <el-radio-group v-model="auditForm.result">
              <el-radio label="通过">通过</el-radio>
              <el-radio label="拒绝">拒绝</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="audit-dialog-footer">
        <el-button @click="auditDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleAudit">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 文件信息编辑对话框 -->
    <el-dialog
      title="文件信息"
      :visible.sync="fileInfoDialogVisible"
      width="40%">
      <el-form :model="fileInfoForm" label-width="100px">
        <el-form-item label="型号">
          <el-input v-model="fileInfoForm.型号" placeholder="请输入产品型号"></el-input>
        </el-form-item>
        <el-form-item label="名称">
          <el-input v-model="fileInfoForm.名称" placeholder="请输入产品名称"></el-input>
        </el-form-item>
        <el-form-item label="售前/售后">
          <el-select v-model="fileInfoForm.售前售后" placeholder="请选择" style="width: 100%">
            <el-option label="售前" value="售前"></el-option>
            <el-option label="售后" value="售后"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="可接入软件">
          <el-input v-model="fileInfoForm.可接入软件" placeholder="请输入可接入软件"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer">
        <el-button @click="fileInfoDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="confirmFileInfo">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 预览抽屉 -->
    <el-drawer
      :title="previewData.name"
      :visible.sync="previewDrawerVisible"
      direction="rtl"
      size="50%">
      <div v-if="previewData" style="padding-left: 20px;padding-right: 20px;">
        <div v-for="(item, index) in previewData.q" :key="index" class="preview-card-container">
          <el-card class="preview-card">
            <div class="IDstyle">
              ID: {{ item[0] }}
            </div>
            <div @click="showPreviewQA(item)">
              <div v-html="marked(item[1])"></div>
              <div v-html="marked(item[2])"></div>
            </div>
            <div class="delete-icon" @click="deletePreviewItem(index)" v-if="previewData.audit == 0">
              <i class="el-icon-delete"></i>
            </div>
          </el-card>
        </div>
      </div>
    </el-drawer>

    <!-- 预览模态框 -->
    <el-dialog
      :title="previewData.name"
      :visible.sync="previewModalVisible"
      width="60%"
      :before-close="handlePreviewModalClose">
      <el-tabs v-model="activeTab" class="custom-tabs">
        <el-tab-pane label="分块内容" name="content">
          <el-row :gutter="20">
            <el-col :span="12">
              <div style="padding-bottom: 15px;">主要内容</div>
              <el-input
                v-model="currentItem[1]"
                type="textarea"
                :rows="20"
                placeholder="该输入框是必填项，该内容通常是对于知识点的描述，也可以是用户的问题，最多 1800 字。">
              </el-input>
            </el-col>
            <el-col :span="12">
              <div style="margin-bottom: 15px;">辅助数据</div>
              <el-input
                v-model="currentItem[2]"
                type="textarea"
                :rows="20"
                placeholder="该部分为可选填项，通常是为了与前面的【数据内容】配合，构建结构化提示词，用于特殊场景，最多 2700 字。">
              </el-input>
            </el-col>
          </el-row>
        </el-tab-pane>
        <el-tab-pane :label="`数据索引(${currentItem[3] ? currentItem[3].length : 0})`" name="index">
          <div style="margin-bottom: 15px;" v-if="previewData.audit == 0">
            <el-button type="primary" icon="el-icon-plus" @click="addNewIndex">新增索引</el-button>
          </div>
          <div v-for="(item, index) in currentItem[3]" :key="index" class="index-item">
            <!-- 默认索引 -->
            <div v-if="item && (item.defaultIndex == true || item.type == 'default')" class="default-index">
              <div class="index-content">
                <div>默认索引</div>
                <div>无法编辑，默认索引会使用【相关数据内容】与【辅助数据】的文本直接生成索引。</div>
              </div>
            </div>

            <!-- 自定义索引 -->
            <div v-else class="custom-index">
              <div @click="startEditing(index)">自定义索引{{ index }}</div>
              <div v-if="!item.editing" class="index-content" @click="startEditing(index)">
                <div>{{ item.text }}</div>
              </div>
              <el-input
                v-else
                v-model="item.text"
                type="textarea"
                :rows="2"
                placeholder="请输入索引内容"
              ></el-input>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
      <span slot="footer" v-if="previewData.audit == 0">
        <el-button @click="previewModalVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmUpdate">确认更新</el-button>
      </span>
    </el-dialog>

  </div>

  <!-- 引入组件库 -->
  <script src="/media/js/vue.js"></script>
  <script src="/media/js/index.js"></script>
  <script src="/media/js/dayjs.min.js"></script>
  <script src="/media/js/axios.min.js"></script>
  <script src="/media/js/marked.min.js"></script>
  <script>
    new Vue({
      el: '#app',
      data() {
        return {
          activeMenu: '2',
          searchKey: '',
          dateRange: [],
          files: [
            {
              name: '2023销售报告.xlsx',
              size: 2456789,
              time: '2023-12-15T14:30:00',
              audit: 0 // 未审核
            }
          ], // 文件数据
          sfilesh: false, // 筛选条件数组
          loginDialogVisible: false,
          loginForm: {
            username: '', // 账号
            password: ''  // 密码
          },
          token: '', // 用户token
          filteredFiles: [],
          loading: false,
          deleteDialogVisible: false,
          currentFile: null,
          pickerOptions: {
            shortcuts: [{
              text: '本周',
              onClick(picker) {
                const end = new Date()
                const start = new Date()
                start.setDate(start.getDate() - start.getDay() + 1)
                picker.$emit('pick', [start, end])
              }
            }, {
              text: '本月',
              onClick(picker) {
                const end = new Date()
                const start = new Date()
                start.setDate(1)
                picker.$emit('pick', [start, end])
              }
            }]
          },
          importDialogVisible: false,
          selectedLibrary: '',
          libraries: [],
          uploadProgress: 0,
          uploadStatus: '',
          previewDrawerVisible: false,
          previewData: {name: '', q: []},
          previewModalVisible: false,
          activeTab: 'content',
          currentItem: ['','', '', []],
          //审核文件
          auditDialogVisible: false, // 控制审核对话框的显示
          auditForm: {
            result: '通过' // 审核结果，默认为“通过”
          },
          currentAuditFile: null, // 当前审核的文件
          selectedFiles: [], // 存储用户选择的文件
          filterStatus: '', // 文件状态过滤条件
          processingMethod: 'chunk', // 处理方式
          processingParam: 'auto',   // 处理参数
          chunkLength: 512,         // 分块长度
          customSeparator: '',       // 自定义分隔符
          serverIP: '',
          sameNameFiles: [], // 存储同名文件列表
          tableHeight: 300, // 初始表格高度
          // 文件信息相关
          fileInfoDialogVisible: false, // 控制文件信息对话框的显示
          fileInfoForm: {
            型号: '',
            名称: '',
            售前售后: '',
            可接入软件: ''
          },
          currentUploadingFile: null // 当前正在上传的文件
        }
      },
      filters: {
        formatSize(size) {
          const units = ['B', 'KB', 'MB', 'GB']
          let i = 0
          while (size >= 1024 && i < units.length - 1) {
            size /= 1024
            i++
          }
          return `${size.toFixed(1)} ${units[i]}`
        },
        formatTime(time) {
          return dayjs(time).format('YYYY-MM-DD HH:mm')
        }
      },
      methods: {
        handleInput(val) {
          if (val == "qa"){
            this.chunkLength = 4000;
          }else{
            this.chunkLength = 512;
          }
        },
        handleLoginClick(e) {
          if (e=="logon"){
            this.loginDialogVisible = true; // 显示登录对话框
          } else{
            this.token = ''; // 清空 token
            this.sfilesh = false;
            localStorage.removeItem('token'); // 清除本地存储的 token
            localStorage.removeItem('sfilesh'); // 清除本地存储的 sfilesh
            this.$message.success('退出成功');
            this.loadFiles(); // 重新加载文件列表
          }
        },
        async handleLogin() {
          try {
            // 调用后台登录接口
            const response = await axios.post('/login/', {
              username: this.loginForm.username,
              password: this.loginForm.password
            });

            if (response.data.code === 200) {
              this.$message.success('登录成功');
              this.sfilesh = true;
              this.token = response.data.token; // 保存 token 到 Vue 实例的 data 中
              this.loginDialogVisible = false; // 关闭登录对话框
              // 写入本地存储，用于后续请求携带 token
              localStorage.setItem('token', response.data.token);
              localStorage.setItem('sfilesh', true);
              this.loadFiles(); // 重新加载文件列表
            } else {
              this.$message.error('登录失败：' + response.data.message);
            }
          } catch (error) {
            console.error('登录失败:', error);
            this.$message.error('登录失败');
          }
        },
        truncate(str) {
          return str.length > 20 ? str.slice(0, 20) + '...' : str
        },
        handleMenuSelect(index) {
          // 处理菜单切换逻辑
          if (index === '2') { // 数据看板的 index 为 '2'
              this.loadFiles();
              this.activeMenu = index
          }else if (index === '1') { // 数据中心的 index 为 '1'
            window.open('http://'+this.serverIP+'/h5_log/', '_blank'); // 打开新页面
          }else if (index === '3') { // 数据中心的 index 为 '3'
            window.open('http://'+this.serverIP+'/h5_images/', '_blank'); // 打开新页面
          }else if (index === '4') { // 数据中心的 index 为 '4'
            window.open('http://'+this.serverIP+'/h5_images_audit_wh/', '_blank'); // 打开新页面
          }

        },
        handleSearch() {
          this.filteredFiles = this.files.filter(file =>
            file.name.toLowerCase().includes(this.searchKey.toLowerCase())
          )
        },
        deleteFile(file) {
          this.currentFile = file
          this.deleteDialogVisible = true
        },
        async confirmDelete() {
          // 执行删除操作
          this.loading = true
          try {
            const response = await axios.get('/deleteCollection/', { params: { collectionId: this.currentFile.id } });
            if (response.data.code === 200) {
                this.$message.success('删除成功');
                this.loadFiles(); // 重新加载文件列表
            } else {
              this.$message.error('删除失败');
            }
          } catch (error) {
              console.error('删除文件失败:', error);
              this.$message.error('删除文件失败');
          } finally {
              this.loading = false;
              this.deleteDialogVisible = false
          }
        },
        async loadFiles(params = {keyword:"",startDate:"",endDate:"",status:"",dataset:"" }) {
          this.loading = true
          try {
            // 调用 getCollectionListInfo 接口获取数据
            const response = await axios.get('/getCollectionListInfo/', {params: params});
            if (response.data.code === 200) {
                this.files = response.data.data; // 将返回的数据赋值给 files
                this.filteredFiles = this.files; // 同时更新 filteredFiles
                this.serverIP = response.data.serverIP;
            }
          } catch (error) {
              console.error('加载文件失败:', error);
              this.$message.error('加载文件失败');
          } finally {
              this.loading = false;
          }
        },
        handleQuery() {
            const params = {
                keyword: this.searchKey,
                startDate: this.dateRange[0] ? dayjs(this.dateRange[0]).format('YYYY-MM-DD') : '',
                endDate: this.dateRange[1] ? dayjs(this.dateRange[1]).format('YYYY-MM-DD') : '',
                status: this.filterStatus, // 添加状态过滤条件
                dataset: this.selectedLibrary,
            }
            console.log('查询参数:', params)
            // 这里可以添加实际的API请求逻辑
            this.loadFiles(params)
        },
        beforeUpload(file) {
            if (!this.selectedLibrary) {
                this.$message.error('请先选择知识库')
                return false
            }
                // 自定义参数时的验证
            if(this.processingParam === 'custom') {
                const min = this.processingMethod === 'chunk' ? 100 : 4000
                const max = this.processingMethod === 'chunk' ? 1800 : 16000

                if(!this.chunkLength || this.chunkLength < min || this.chunkLength > max) {
                    this.$message.error(`分块长度需在${min}-${max}之间`)
                    return false
                }
            }
            return true
        },
        handleRemove(file, fileList) {
          console.log(file, fileList);
          },
        handlePreview(file) {
          console.log(file);
        },
        handleFileChange(file, fileList) {
          this.selectedFiles = fileList;
        },
        // 上传所有文件
        async uploadAllFiles() {
          if (this.selectedFiles.length === 0) {
            this.$message.warning('请先选择文件');
            return;
          }
          for (let i = 0; i < this.selectedFiles.length; i++) {
            const file = this.selectedFiles[i];
            try {
              await this.customUpload(file);
              this.selectedFiles.splice(i, 1); // 上传成功后移除文件
              i--; // 调整索引
            } catch (error) {
              console.error('上传失败:', error);
              this.$message.error(`文件 ${file.name} 上传失败`);
            }
          }
        },
        async showFileInfoDialog(file) {
          try {
            // 调用后端接口获取文件信息
            const response = await axios.get('/getFileInfo/', { params: { filename: file.name } });
            if (response.data.code === 200) {
              const fileInfo = response.data.data;

              // 显示文件信息编辑对话框
              this.fileInfoForm = {
                型号: fileInfo.型号 || '',
                名称: fileInfo.名称 || '',
                售前售后: fileInfo.售前售后 || '',
                可接入软件: fileInfo.可接入软件 || ''
              };

              this.fileInfoDialogVisible = true;
              this.currentUploadingFile = file;
            } else {
              // 如果接口调用失败，直接上传文件
              await this.doUpload(file);
            }
          } catch (error) {
            console.error('获取文件信息失败:', error);
            // 如果接口调用失败，直接上传文件
            await this.doUpload(file);
          }
        },
        async confirmFileInfo() {
          // 确认文件信息，继续上传
          this.fileInfoDialogVisible = false;
          await this.doUpload(this.currentUploadingFile, this.fileInfoForm);
        },
        async doUpload(file, fileInfo = null) {
          this.uploadProgress = 0;
          const formData = new FormData();
          formData.append('file', file.raw);
          formData.append('type', this.selectedLibrary);
          // 添加处理参数
          const processData = {
            trainingType: this.processingMethod,
            chunkSize: this.processingParam === 'auto' ?
                      (this.processingMethod === 'chunk' ? 512 : 4096) :
                      this.chunkLength,
            chunkSplitter: this.processingParam === 'custom' ? this.customSeparator : '',
            processingParam: this.processingParam
          }

          // 如果有文件信息，添加到处理参数中
          if (fileInfo) {
            processData.fileInfo = fileInfo;
          }

          formData.append('data', JSON.stringify(processData))
          try {
            // 使用定时器模拟上传进度
            let interval = setInterval(() => {
              console.log(this.uploadProgress);
              if (this.uploadProgress < 90) {
                this.uploadProgress += 10;
              } else {
                clearInterval(interval);
              }
            }, 1000);
            const response = await axios.post('/uploadfiles/', formData);
            this.uploadProgress = 100
            this.handleUploadSuccess(); // 修改：仅在接口返回成功后调用
            clearInterval(interval);
          } catch (error) {
            this.handleUploadError();
            clearInterval(interval);
          } finally {
          }
        },
        async customUpload(file) {
          // 先弹出文件信息编辑框
          await this.showFileInfoDialog(file);
        },
        handleProgress(event) {
            this.uploadProgress = Math.floor(event.percent)
        },
        handleUploadSuccess() { // 修改：移除参数 res
            this.uploadStatus = 'success'
            this.$message.success('上传成功')
            // this.importDialogVisible = false
            this.loadFiles()
        },
        handleUploadError() {
            this.uploadStatus = 'exception'
            this.$message.error('上传失败')
        },
        handleOpenImport() {
          this.uploadProgress = 0;
          this.uploadStatus = ''
          //this.fetchLibraries();
          this.importDialogVisible = true;
        },
        async fetchLibraries() {
          try {
            const response = await fetch('/getDatasList/');
            const data = await response.json();
            if(data.code === 200) {
              this.libraries = data.data.map(item => ({
                id: item.id,
                name: item.name
              }));
            }
          } catch (error) {
            console.error('获取知识库失败:', error);
            this.$message.error('获取知识库列表失败');
          }
        },
        marked(text) {
          return marked.parse(text); // 调用全局的 marked 函数
        },
        async previewFile(file) {
          try {
            const response = await axios.get('/getDatasetdatas/', { params: { collectionId: file.id } });
            if (response.data.code === 200) {
              this.previewData = response.data.data;
              this.previewDrawerVisible = true;
            }
          } catch (error) {
            console.error('获取文件数据失败:', error);
            this.$message.error('获取文件数据失败');
          }
        },
        // 删除预览数据中的项
        async deletePreviewItem(index) {
          try {
            const confirmResult = await this.$confirm('确认删除该条数据吗？', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning' // 修改为警告类型
            });

            if (confirmResult === 'confirm') {
              const response = await axios.post('/deleteQA/', { id: this.previewData.q[index][0] });
              if (response.data.code === 200) {
                this.previewData.q.splice(index, 1); // 从预览数据中删除该项
                this.$message.success('删除成功');
              } else {
                this.$message.error('删除失败');
              }
            }
          } catch (error) {
            if (error !== 'cancel') { // 如果不是用户取消操作
              console.error('删除失败:', error);
              this.$message.error('删除失败');
            } else {
              this.$message.info('已取消删除');
            }
          }
        },
        // 预览同名文件
        previewSameNameFile(file) {
          this.previewFile(file);
        },
          // 删除同名文件
        async deleteSameNameFile(file) {
          try {
            const confirmResult = await this.$confirm('确认删除该文件吗？', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            });

            if (confirmResult === 'confirm') {
              const response = await axios.get('/deleteCollection/', {
                params: { collectionId: file.id }
              });
              if (response.data.code === 200) {
                this.$message.success('删除成功');
                // 从列表中移除已删除的文件
                this.sameNameFiles = this.sameNameFiles.filter(f => f.id !== file.id);
              }
            }
          } catch (error) {
            if (error !== 'cancel') {
              console.error('删除失败:', error);
              this.$message.error('删除失败');
            }
          }
        },
          // 调整表格高度
        adjustTableHeight() {
          const dialogHeight = this.$el.querySelector('.audit-dialog').offsetHeight;
          const headerHeight = this.$el.querySelector('.audit-dialog .el-dialog__header').offsetHeight;
          const formItemHeight = this.$el.querySelector('.audit-dialog .el-form-item').offsetHeight;
          const footerHeight = this.$el.querySelector('.audit-dialog-footer').offsetHeight;

          // 计算可用高度 = 对话框高度 - 标题高度 - 表单项高度 - 底部高度 - 内边距
          const availableHeight = dialogHeight - headerHeight - formItemHeight - footerHeight - 40;
          this.tableHeight = Math.max(200, availableHeight); // 最小高度200px
        },
        async auditFile(file) {
          this.currentAuditFile = file; // 保存当前审核的文件
          this.auditDialogVisible = true; // 显示审核对话框
          // 获取同名文件列表
          if (file.cpxh){
            try {
              const response = await axios.get('/getSameNameFiles/', {
                params: {
                  cpxh: file.cpxh,
                  excludeId: file.id
                }
              });
              if (response.data.code === 200) {
                this.sameNameFiles = response.data.data;
                // 根据文件数量动态调整表格高度
                // this.$nextTick(() => {
                //   this.adjustTableHeight();
                // });
              }
            } catch (error) {
              console.error('获取同名文件失败:', error);
              this.$message.error('获取同名文件失败');
            }
          }
        },
        async handleAudit() {
          try {
            if (this.auditForm.result === '通过') {
              // 调用后台审核通过接口
              const response = await axios.post('/auditCollection/', {collectionId: this.currentAuditFile.id,token: this.token});
              if (response.data.code === 200) {
                this.$message.success('审核通过');
                this.loadFiles(); // 重新加载文件列表
              } else {
                this.$message.error('审核失败：' + response.data.message);
              }
            } else {
              // 处理拒绝逻辑
              this.currentFile = this.currentAuditFile; // 保存当前文件信息，以便在拒绝时使用
              this.confirmDelete(); // 调用删除文件的函数
              this.$message.info('文件已拒绝');
            }
          } catch (error) {
            console.error('审核失败:', error);
            this.$message.error('审核失败');
          } finally {
            this.auditDialogVisible = false;
          }
        },
        // 更新知识片段内容的函数
        showPreviewQA(item) {
          this.previewModalVisible = true; // 控制新页面框的显示
          this.currentItem = item; // 初始化当前编辑的数据
        },
        handlePreviewModalClose() {
          this.previewModalVisible = false;
          this.currentItem = ['','', '', []];
        },
        startEditing(index) {
          this.currentItem[3][index].editing = !this.currentItem[3][index].editing;
          console.log('开始编辑', this.currentItem[3]);
        },
        addNewIndex() {
          this.currentItem[3].push({"text":"","editing":true}); // 添加一个新的自定义索引
        },
        confirmUpdate() {
          // 调用后台接口更新数据
          this.updateData();
        },
        async updateData() {
          try {
            // 调用后端接口更新数据
            const response = await axios.post('/updateDatasetdatas/', {data: this.currentItem});
            if (response.data.code === 200) {
              this.$message.success('更新成功');
              this.previewModalVisible = false; // 关闭模态框
            } else {
              this.$message.error('更新失败');
            }
          } catch (error) {
            console.error('更新数据失败:', error);
            this.$message.error('更新数据失败');
          }
        },
      },
      mounted() {
        this.token = localStorage.getItem('token') || '';
        this.sfilesh = localStorage.getItem('sfilesh') || false;
        // 设置默认日期范围为当前月的第一天到今天
        const ett = new Date();
        const stt = new Date(ett.getFullYear(), ett.getMonth(), 1);
        this.dateRange = [stt, ett];
        let pp = {keyword:"",startDate:dayjs(stt).format('YYYY-MM-DD'),endDate:dayjs(ett).format('YYYY-MM-DD'),status:"",dataset:""}
        this.fetchLibraries();
        this.loadFiles(pp)
      }
    })
  </script>
</body>
</html>